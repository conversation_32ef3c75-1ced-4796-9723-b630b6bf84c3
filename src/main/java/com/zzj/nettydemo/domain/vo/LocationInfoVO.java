package com.zzj.nettydemo.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-07-22
 * 位置信息VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LocationInfoVO {

    /**
     * 终端手机号
     */
    private String phoneNumber;

    /**
     * 原始纬度（度）- WGS84坐标系
     */
    @JsonIgnore
    private double latitude;

    /**
     * 原始经度（度）- WGS84坐标系
     */
    @JsonIgnore
    private double longitude;

    /**
     * 转换后纬度（度）- GCJ02坐标系（火星坐标系）
     */
    private double gcj02Latitude;

    /**
     * 转换后经度（度）- GCJ02坐标系（火星坐标系）
     */
    private double gcj02Longitude;

    /**
     * 高程（米）
     */
    private int altitude;

    /**
     * 速度（km/h）
     */
    private double speed;

    /**
     * 方向（度）
     */
    private int direction;

    /**
     * 时间
     */
    private LocalDateTime time;

    /**
     * 报警标志
     */
    @JsonIgnore
    private int alarmFlag;

    /**
     * 状态
     */
    @JsonIgnore
    private int status;

    /**
     * 数据接收时间
     */
    @JsonIgnore
    private LocalDateTime receivedTime;
}
