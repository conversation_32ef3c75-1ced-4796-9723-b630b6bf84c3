package com.zzj.nettydemo.jt808.codec;

import com.zzj.nettydemo.jt808.message.MessageBuilder;
import com.zzj.nettydemo.jt808.message.Message;
import com.zzj.nettydemo.jt808.message.header.AbstractMessageHeader;
import com.zzj.nettydemo.jt808.message.header.MessageHeaderFactory;
import com.zzj.nettydemo.jt808.protocol.JT808ConstantsAndConfig;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageDecoder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * JT808消息解析器
 * 负责将解码后的数据包解析为具体的Message对象
 * 支持分包消息的重组处理
 * <AUTHOR>
 */
@Slf4j
public class JT808MessageParser extends MessageToMessageDecoder<ByteBuf> {

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf msg, List<Object> out) throws Exception {
        // 至少需要包含消息头（至少17字节）
        if (msg.readableBytes() < 17) {
            log.warn("消息长度不足，无法解析消息头: {} 字节", msg.readableBytes());
            return;
        }

        // 将ByteBuf转换为byte数组
        byte[] data = new byte[msg.readableBytes()];
        msg.getBytes(msg.readerIndex(), data);

        //判断是否分包
        int hasSubPackage = ((data[2]<<8)|data[3]) >> 13 & 0x01;

        //不需要分包，直接解析
        if(hasSubPackage==0){
            Message message = MessageBuilder.parse(data);
            out.add(message);
            return;
        }

        // 如果是分包消息，需要进行分包重组
        // 解析出消息头
        AbstractMessageHeader header = MessageHeaderFactory.createHeaderFromRawData(data);
        header.decode(data);

        //创建分包管理器
        JT808PackageManager packageManager = ctx.channel().attr(JT808ConstantsAndConfig.PACKAGE_MANAGER).get();
        if(packageManager == null){
            packageManager = new JT808PackageManager();
            ctx.channel().attr(JT808ConstantsAndConfig.PACKAGE_MANAGER).set(packageManager);
        }

        packageManager.addPackage(header.getFlowId(), header.getPacketFragment().getTotalPackages(),
                header.getPacketFragment().getPackageSequence(), data);

        boolean complete = packageManager.isComplete(header.getFlowId());
        if(complete){
            byte[][] completeData = packageManager.getCompleteRawData(header.getFlowId());
            for (byte[] bytes : completeData) {
                Message message = MessageBuilder.parse(bytes);
                out.add(message);
            }
            packageManager.removePackage(header.getFlowId());
        }

    }

    //todo 拼凑完整的消息，后面看看具体格式
}
