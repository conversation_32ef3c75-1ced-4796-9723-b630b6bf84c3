package com.zzj.nettydemo.jt808.codec;

import com.zzj.nettydemo.jt808.protocol.JT808ConstantsAndConfig;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * JT808分包管理器
 * 负责处理分包消息的接收、重组和超时清理
 * <AUTHOR>
 */
@Slf4j
public class JT808PackageManager {
    // 键：消息流水号_分包包内序号
    // 值：分包数据的完整原始byte[]
    // todo 其实保存消息体即可，后续可以优化。
    private final Map<String, byte[]> packageMap = new ConcurrentHashMap<>();

    // 存储分包元信息：消息流水号 -> 分包信息
    private final Map<Integer, PackageInfo> packageInfoMap = new ConcurrentHashMap<>();

    //todo 某些可能需要定时清理

    /**
     * 添加分包
     *
     * @param flowId       消息流水号
     * @param totalPackage 总包数
     * @param packageIndex 分包序号
     * @param rawData      分包的完整原始数据
     * @return 是否所有分包都已接收
     */
    public boolean addPackage(int flowId, int totalPackage, int packageIndex, byte[] rawData) {
        // 验证参数
        if (totalPackage <= 0 || totalPackage > JT808ConstantsAndConfig.MAX_PACKAGE_COUNT) {
            log.warn("无效的总包数: {}", totalPackage);
            return false;
        }

        if (packageIndex <= 0 || packageIndex > totalPackage) {
            log.warn("无效的包序号: {}, 总包数: {}", packageIndex, totalPackage);
            return false;
        }

        // 根据注释：键为消息流水号_分包包内序号
        String key = flowId + "_" + packageIndex;

        if (packageMap.containsKey(key)) {
            log.warn("重复的分包: flowId={}, packageIndex={}", flowId, packageIndex);
            return false;
        }

        // 存储分包原始数据
        packageMap.put(key, rawData);

        // 更新分包信息
        PackageInfo packageInfo = packageInfoMap.computeIfAbsent(flowId,
                k -> new PackageInfo(totalPackage));
        packageInfo.addReceivedPackage(packageIndex);

        log.debug("接收到分包消息: 流水号={}, 总包数={}, 包序号={}, 已接收={}/{}",
                flowId, totalPackage, packageIndex,
                packageInfo.getReceivedCount(), totalPackage);

        return packageInfo.isComplete();
    }

    /**
     * 获取完整的原始数据数组
     *
     * @param flowId 消息流水号
     * @return 按顺序排列的所有分包原始数据
     */
    public byte[][] getCompleteRawData(int flowId) {
        PackageInfo packageInfo = packageInfoMap.get(flowId);

        if (packageInfo == null || !packageInfo.isComplete()) {
            log.warn("分包信息不存在或未完成: flowId={}", flowId);
            return null;
        }

        byte[][] result = new byte[packageInfo.getTotalPackage()][];

        for (int i = 1; i <= packageInfo.getTotalPackage(); i++) {
            String key = flowId + "_" + i;
            byte[] data = packageMap.get(key);
            if (data != null) {
                result[i - 1] = data;
            } else {
                log.error("分包数据丢失: flowId={}, packageIndex={}", flowId, i);
                return null;
            }
        }

        log.debug("分包重组完成: flowId={}, 总包数={}", flowId, packageInfo.getTotalPackage());
        return result;
    }

    /**
     * 移除已处理的包
     *
     * @param flowId 消息流水号
     */
    public void removePackage(int flowId) {
        PackageInfo packageInfo = packageInfoMap.remove(flowId);

        if (packageInfo != null) {
            // 移除所有相关的分包数据
            for (int i = 1; i <= packageInfo.getTotalPackage(); i++) {
                String key = flowId + "_" + i;
                packageMap.remove(key);
            }
            log.debug("清理分包数据: 流水号={}, 总包数={}", flowId, packageInfo.getTotalPackage());
        }
    }

    public boolean isComplete(int flowId) {
        PackageInfo packageInfo = packageInfoMap.get(flowId);
        if (packageInfo != null) {
            return packageInfo.getReceivedCount()==packageInfo.getTotalPackage();
        }
        return false;
    }


    /**
     * 分包信息类
     */
    @Getter
    @Setter
    private static class PackageInfo {
        private final int totalPackage;
        private final boolean[] receivedPackages;
        //        private final long createTime;
        private int receivedCount = 0;

        public PackageInfo(int totalPackage) {
            this.totalPackage = totalPackage;
            this.receivedPackages = new boolean[totalPackage];
//            this.createTime = System.currentTimeMillis();
        }

        public void addReceivedPackage(int packageIndex) {
            // 分包索引从1开始
            if (packageIndex >= 1 && packageIndex <= totalPackage
                    && !receivedPackages[packageIndex - 1]) {
                receivedPackages[packageIndex - 1] = true;
                receivedCount++;
            }
        }

        public boolean isComplete() {
            return receivedCount == totalPackage;
        }

    }
}
