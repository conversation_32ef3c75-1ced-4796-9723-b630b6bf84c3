package com.zzj.nettydemo.jt808.codec;

import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.buffer.ByteBufUtil;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import io.netty.handler.codec.CorruptedFrameException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class JT808Decoder extends ByteToMessageDecoder {
    // JT808协议标识位
    private static final byte FLAG = 0x7e;
    // 转义相关
    private static final byte ESCAPE_CHAR = 0x7d;
    //7d ---> 0x7d 0x01
    private static final byte ESCAPE_CHAR_7D = 0x01;
    //7e ---> 0x7d 0x02
    private static final byte ESCAPE_CHAR_7E = 0x02;

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        // 检查输入缓冲区的有效性
        if (in == null || in.refCnt() <= 0) {
            log.warn("输入缓冲区无效，跳过解码");
            return;
        }

        try {
            // 拆分粘包
            // 半包没有完整的两个首尾,遍历缓冲区时，不会取出数据，而是等待下一个
            while (in.readableBytes() >= 2) {
            // 标记当前读取位置，用于回滚
            in.markReaderIndex();

            // 1. 找起始标识位 0x7e
            int startIndex = findFlag(in, in.readerIndex());
            if (startIndex == -1) {
                in.resetReaderIndex(); // 无起始位，回退等待更多数据
                return;
            }

            // 2. 跳过起始位，找结束标识位 0x7e
            in.readerIndex(startIndex + 1);

            // 3.查找结束标识位
            int endIndex = findFlag(in, in.readerIndex());

            // 没有找到结束标识位，重置到起始标识位之后，等待更多数据
            if (endIndex == -1) {
                in.readerIndex(startIndex + 1);
                return;
            }

            // 提取数据包（不包括起始和结束标识位）
            // 提取 [start+1, end-1] 的数据（未还原转义）
            int dataLength = endIndex - in.readerIndex();
            if (dataLength <= 0) {
                // 无效数据包，跳过结束标识位继续查找
                in.readerIndex(endIndex + 1);
                continue;
            }

            // 用 slice 避免内存拷贝
            ByteBuf rawData = in.readSlice(dataLength);
            // 跳过结束位
            in.readerIndex(endIndex + 1);

            // 4. 转义还原（必须先还原，再校验）
            ByteBuf restoredBuf = restoreEscape(ctx.alloc(), rawData);
            // 注意：rawData 是 slice，不需要手动释放，它与原始缓冲区共享内存

            if(!validateChecksum(restoredBuf)){
                restoredBuf.release();
                log.error("校验失败，丢弃数据包");
                continue;
            }

            // 移除校验码，传递有效数据
            int payloadLen = restoredBuf.readableBytes() - 1;
            if (payloadLen > 0) {
                ByteBuf payload = ctx.alloc().buffer(payloadLen);
                payload.writeBytes(restoredBuf, 0, payloadLen);
                out.add(payload);
            } else {
                log.warn("有效数据长度为0");
            }
            restoredBuf.release();
            }
        } catch (Exception e) {
            log.error("JT808解码过程中发生异常: {}", e.getMessage(), e);
            // 发生异常时，跳过当前所有可读数据，避免无限循环
            in.skipBytes(in.readableBytes());
        }
    }

    // 辅助方法：查找标识位 0x7e
    private int findFlag(ByteBuf in, int fromIndex) {
        try {
            // 检查ByteBuf是否有效
            if (in == null || in.refCnt() <= 0) {
                log.warn("尝试在无效的ByteBuf中查找标识位");
                return -1;
            }

            for (int i = fromIndex; i < in.writerIndex(); i++) {
                if (in.getByte(i) == FLAG) {
                    return i;
                }
            }
            return -1;
        } catch (Exception e) {
            log.warn("查找标识位时发生异常: {}", e.getMessage());
            return -1;
        }
    }



    // 转义还原：用 ctx.alloc() 分配缓冲区，适配池化策略
    private ByteBuf restoreEscape(ByteBufAllocator alloc, ByteBuf raw) throws Exception{
        // 预估还原后的缓冲区大小，避免多次扩容
        // 通常转义会减少字节数，预估容量为原长度的80%
        ByteBuf restored = alloc.buffer((int) (raw.readableBytes() * 0.8));

        try {
            byte[] array = ByteBufUtil.getBytes(raw);
            int len=array.length;

            for (int i = 0; i < len; i++) {
                //数据包经过转义后，0x7d不可能单独出现
                if(array[i]==0x7d&&i+1==len) {
                    log.error("转义还原失败，数据包有误");
                    throw new Exception("转义还原失败，数据包有误");
                }
                if(array[i]==0x7d&&array[i+1]==0x01){
                    restored.writeByte(0x7d);
                    i++;
                }else if(array[i]==0x7d&&array[i+1]==0x02){
                    restored.writeByte(0x7e);
                    i++;
                }else{
                    restored.writeByte(array[i]);
                }
            }
            // 调整容量为实际写入大小
            restored.capacity(restored.writerIndex());
            return restored;
        } catch (Exception e) {
            restored.release();
            throw e;
        }
    }

    /**
     * 校验码验证
     */
    private boolean validateChecksum(ByteBuf buf) {
        if (buf.readableBytes() < 2) {
            return false;
        }
        // 获取数据包中的校验码
        byte expected = buf.getByte(buf.readableBytes() - 1);

        // 计算校验和（异或校验）
        byte calculatedChecksum = 0;
        for (int i = 0; i < buf.readableBytes() - 1; i++) {
            calculatedChecksum ^= buf.getByte(i);
        }

        if(calculatedChecksum != expected){
            log.error("校验失败，计算校验码: 0x{}, 数据包校验码: 0x{}", calculatedChecksum, expected);
        }else{
            log.info("校验成功，计算校验码: 0x{}, 数据包校验码: 0x{}", calculatedChecksum, expected);
        }

        return calculatedChecksum == expected;
    }
}
