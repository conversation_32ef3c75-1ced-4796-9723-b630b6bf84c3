package com.zzj.nettydemo.jt808.codec;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import lombok.extern.slf4j.Slf4j;

/**
 * JT808通用消息编码器
 * 负责将消息字节数组编码为符合JT808协议格式的数据包
 *
 * 编码流程：
 * 1. 计算异或校验码
 * 2. 拼接数据包：标识位 + 原数据 + 校验码 + 标识位
 * 3. 转义处理：0x7d -> 0x7d 0x01, 0x7e -> 0x7d 0x02
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@ChannelHandler.Sharable
public class JT808Encoder extends MessageToByteEncoder<byte[]> {

    // JT808协议标识位
    private static final byte FLAG = 0x7e;
    // 转义相关常量
    private static final byte ESCAPE_CHAR = 0x7d;
    // 0x7d 转义为 0x7d 0x01
    private static final byte ESCAPE_CHAR_7D = 0x01;
    // 0x7e 转义为 0x7d 0x02
    private static final byte ESCAPE_CHAR_7E = 0x02;

    @Override
    protected void encode(ChannelHandlerContext ctx, byte[] msg, ByteBuf out) {
        log.error("-----------------------------------------------------------------------------------");
        log.info("🔄 JT808编码器：开始编码，待编码消息长度: {} 字节", msg.length);


        if (msg.length == 0) {
            log.warn("待编码的消息为空，跳过编码");
            return;
        }


        try {

            // 1. 计算校验码（对原始消息数组进行异或运算）
            byte checksum = calculateChecksum(msg);

            // 2. 拼接最终数据包：标识位 + 原数据 + 校验码 + 标识位
            byte[] data = new byte[msg.length + 3];
            data[0] = FLAG;
            System.arraycopy(msg, 0, data, 1, msg.length);
            data[msg.length + 1] = checksum;
            data[msg.length + 2] = FLAG;

            // 修复：正确格式化字节数组为十六进制字符串
            log.info("🔄 JT808编码器：编码完成，原始长度: {}, 编码后长度: {} 字节",
                     msg.length, data.length);
            log.debug("🔄 JT808编码器：编码后消息内容: {}", bytesToHex(data));


            out.writeBytes(data);



        } catch (Exception e) {
            log.error("JT808消息编码失败: {}", e.getMessage(), e);
            throw new RuntimeException("JT808消息编码失败", e);
        }
    }

    /**
     * 计算异或校验码
     * 从消息头首字节开始，与后一字节依次进行异或操作，直至消息体末字节
     *
     * @param data 原始消息数据（消息头+消息体）
     * @return 校验码
     */
    private byte calculateChecksum(byte[] data) {
        byte checksum = 0;
        for (byte b : data) {
            checksum ^= b;
        }

        log.debug("计算校验码: 0x{}", String.format("%02X", checksum & 0xFF));
        return checksum;
    }

    /**
     * 数据转义处理
     * 转义规则：
     * - 0x7d -> 0x7d 0x01
     * - 0x7e -> 0x7d 0x02
     *
     * @param data 待转义的数据
     * @return 转义后的数据
     */
    private byte[] escapeData(byte[] data) {
        // 预估转义后的数据长度（最坏情况下每个字节都需要转义，长度翻倍）
        // 但实际情况下转义字节很少，这里预估增加50%的容量
        int estimatedLength = (int) (data.length * 1.5);
        byte[] escaped = new byte[estimatedLength];
        int writeIndex = 0;

        for (byte b : data) {
            // 确保数组容量足够
            if (writeIndex >= escaped.length - 1) {
                // 扩容
                byte[] newEscaped = new byte[escaped.length * 2];
                System.arraycopy(escaped, 0, newEscaped, 0, writeIndex);
                escaped = newEscaped;
            }

            if (b == 0x7d) {
                escaped[writeIndex++] = ESCAPE_CHAR;
                escaped[writeIndex++] = ESCAPE_CHAR_7D;
            } else if (b == 0x7e) {
                escaped[writeIndex++] = ESCAPE_CHAR;
                escaped[writeIndex++] = ESCAPE_CHAR_7E;
            } else {
                escaped[writeIndex++] = b;
            }
        }

        // 创建实际长度的数组
        byte[] result = new byte[writeIndex];
        System.arraycopy(escaped, 0, result, 0, writeIndex);

        log.debug("数据转义完成，原始长度: {}, 转义后长度: {}", data.length, result.length);
        return result;
    }

    /**
     * 字节数组转十六进制字符串
     * 将字节数组转换为十六进制格式的字符串，方便日志打印和调试
     *
     * @param bytes 待转换的字节数组
     * @return 十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString().trim();
    }

}
