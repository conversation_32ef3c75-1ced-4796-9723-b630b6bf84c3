package com.zzj.nettydemo.jt808.protocol;

import com.zzj.nettydemo.jt808.codec.JT808PackageManager;
import io.netty.util.AttributeKey;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025-07-16
 */
@Configuration
public class JT808ConstantsAndConfig {
    // 协议标识位
    public static final byte FLAG = 0x7e;

    // 转义字符
    public static final byte ESCAPE = 0x7d;
    public static final byte ESCAPE_7D = 0x01;
    public static final byte ESCAPE_7E = 0x02;

    // Channel属性键
    public static final AttributeKey<JT808PackageManager> PACKAGE_MANAGER =
            AttributeKey.valueOf("PACKAGE_MANAGER");

    // 默认字符集
    public static final String DEFAULT_CHARSET = "GBK";

    // 分包超时时间（毫秒）
    public static final long PACKAGE_TIMEOUT_MS = 30000; // 30秒

    // 最大分包数量
    public static final int MAX_PACKAGE_COUNT = 1024;

    //消息分隔符

    //终端应答

    //终端消息分类

    //服务器应答

}
