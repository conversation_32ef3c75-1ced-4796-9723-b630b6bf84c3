package com.zzj.nettydemo.jt808.protocol;

import lombok.extern.slf4j.Slf4j;

/**
 * JT808协议版本检测器
 * 统一版本检测逻辑，消除代码重复
 * <AUTHOR>
 */
@Slf4j
public class VersionDetectorUtil {

    /**
     * 从原始数据中检测协议版本
     * @param data 原始数据包
     * @return 协议版本
     * @throws Exception 不支持的版本
     */
    public static Jt808Version detectVersionFromRawData(byte[] data) throws Exception{
        if (data == null || data.length < 5) {
            throw new Exception("数据包长度不足，无法检测版本");
        }

        // 解析消息体属性 (第3-4字节)
        // 版本标识位在第3个字节的第14位 (从0开始计数)
        int attributes = (data[2] << 8) | data[3] ;

        // 2019版本及以后引入了版本标识位
        // 版本号在第5字节 (索引4)
        //如果是2013的版本，那么data[4]为手机号，无用数据
        return detectVersion(attributes, data[4]);
    }

    /**
     * 从消息体属性和协议版本号检测协议版本
     */
    private static Jt808Version detectVersion(int attributes, byte versionNumber) throws Exception{
        int versionFlag = (attributes >> 14) & 0x01;

        // 2013版本未引入版本标识位
        if (versionFlag == 0) {
            log.debug("检测到JT808 2013版本协议");
            return Jt808Version.V2013;
        }

        if (versionNumber == 1) {
            log.debug("检测到JT808 2019版本协议 (版本号=1)");
            return Jt808Version.V2019;
        } else {
            throw new Exception(String.format("协议版本检测失败，不支持的协议版本号: %d", versionNumber));
        }
    }


}
