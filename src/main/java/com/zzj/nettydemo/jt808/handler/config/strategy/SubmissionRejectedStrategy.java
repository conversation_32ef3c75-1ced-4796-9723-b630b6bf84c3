package com.zzj.nettydemo.jt808.handler.config.strategy;

/**
 * 消息提交拒绝策略接口
 * 当消息提交达到限制时的处理策略
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
public interface SubmissionRejectedStrategy {

    /**
     * 处理拒绝逻辑
     *
     * @param messageType 消息类型
     * @param currentCount 当前提交数量
     * @param maxLimit 最大限制数量
     * @param context 拒绝上下文信息
     * @return true表示允许处理(可能在其他方式下), false表示完全拒绝
     */
    boolean handleRejection(int messageType, int currentCount, int maxLimit, RejectionContext context);

    /**
     * 获取策略名称
     */
    String getStrategyName();

    /**
     * 获取策略描述
     */
    String getDescription();
}
