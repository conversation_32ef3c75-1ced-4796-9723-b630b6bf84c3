//package com.zzj.nettydemo.jt808.handler.executor;
//
//import com.zzj.nettydemo.jt808.handler.message.PriorityMessage;
//import jakarta.annotation.PostConstruct;
//import jakarta.annotation.PreDestroy;
//import lombok.Getter;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//
//import java.util.concurrent.*;
//import java.util.concurrent.atomic.AtomicInteger;
//
///**
// * 统一优先级线程池
// * 所有消息类型共用一个线程池，通过优先级队列控制处理顺序
// * <AUTHOR>
// * @date 2025-07-19
// */
//@Slf4j
//@Component
//public class MessagePriorityThreadPool {
//
//    private ThreadPoolExecutor threadPool;
//    private PriorityBlockingQueue<Runnable> priorityQueue;
//
//    // 线程池配置参数
//    private static final int CORE_POOL_SIZE = 8;
//    private static final int MAXIMUM_POOL_SIZE = 16;
//    private static final long KEEP_ALIVE_TIME = 60L;
//    private static final int INITIAL_QUEUE_CAPACITY = 1000;
//
//    @PostConstruct
//    public void init() {
//        // 创建优先级阻塞队列，按PriorityMessage的优先级排序
//        priorityQueue = new PriorityBlockingQueue<>(INITIAL_QUEUE_CAPACITY, (r1, r2) -> {
//            if (r1 instanceof WrapPriorityTask task1&& r2 instanceof WrapPriorityTask task2) {
//                return task1.getPriorityMessage().compareTo(task2.getPriorityMessage());
//            }
//            return 0;
//        });
//
//        // 创建线程池
//        threadPool = new ThreadPoolExecutor(
//                CORE_POOL_SIZE,
//                MAXIMUM_POOL_SIZE,
//                KEEP_ALIVE_TIME,
//                TimeUnit.SECONDS,
//                priorityQueue,
//                new UnifiedThreadFactory(),
//                new UnifiedRejectedExecutionHandler()
//        );
//
//    }
//
//    /**
//     * 提交优先级任务
//     */
//    public CompletableFuture<Void> submit(PriorityMessage priorityMessage, Runnable task) {
//        WrapPriorityTask wrapPriorityTask = new WrapPriorityTask(priorityMessage, task);
//
//        try {
//            threadPool.execute(wrapPriorityTask);
//            return wrapPriorityTask.getFuture();
//        } catch (RejectedExecutionException e) {
//            log.error("任务被拒绝执行: 消息类型={}, 优先级={}",
//                    priorityMessage.getMessageType(), priorityMessage.getPriority(), e);
//
//            CompletableFuture<Void> future = new CompletableFuture<>();
//            future.completeExceptionally(e);
//            return future;
//        }
//    }
//
//    /**
//     * 获取线程池统计信息
//     */
////    public ThreadPoolStats getStats() {
////        return new ThreadPoolStats(
////                threadPool.getActiveCount(),
////                threadPool.getCorePoolSize(),
////                threadPool.getMaximumPoolSize(),
////                threadPool.getPoolSize(),
////                threadPool.getQueue().size(),
////                threadPool.getCompletedTaskCount(),
////                threadPool.getTaskCount()
////        );
////    }
////
////    /**
////     * 记录统计信息
////     */
////    public void logStats() {
////        ThreadPoolStats stats = getStats();
////        log.info("=== 统一线程池统计 ===");
////        log.info("活跃线程: {}, 核心线程: {}, 最大线程: {}, 当前线程: {}",
////                stats.getActiveCount(), stats.getCorePoolSize(),
////                stats.getMaximumPoolSize(), stats.getPoolSize());
////        log.info("队列大小: {}, 已完成任务: {}, 总任务数: {}",
////                stats.getQueueSize(), stats.getCompletedTaskCount(), stats.getTaskCount());
////    }
//
//    @PreDestroy
//    public void shutdown() {
//        if (threadPool != null) {
//            log.info("正在关闭消息处理线程池...");
//            threadPool.shutdown();
//            try {
//                if (!threadPool.awaitTermination(30, TimeUnit.SECONDS)) {
//                    threadPool.shutdownNow();
//                }
//            } catch (InterruptedException e) {
//                threadPool.shutdownNow();
//                Thread.currentThread().interrupt();
//            }
//            log.info("线程池已关闭");
//        }
//    }
//
//    /**
//     * 优先级任务包装类
//     */
//    private static class WrapPriorityTask implements Runnable {
//        private final PriorityMessage priorityMessage;
//        private final Runnable actualTask;
//        private final CompletableFuture<Void> future;
//
//        public WrapPriorityTask(PriorityMessage priorityMessage, Runnable actualTask) {
//            this.priorityMessage = priorityMessage;
//            this.actualTask = actualTask;
//            this.future = new CompletableFuture<>();
//        }
//
//        @Override
//        public void run() {
//            try {
//                actualTask.run();
//                future.complete(null);
//            } catch (Exception e) {
//                future.completeExceptionally(e);
//            }
//        }
//
//        public PriorityMessage getPriorityMessage() {
//            return priorityMessage;
//        }
//
//        public CompletableFuture<Void> getFuture() {
//            return future;
//        }
//    }
//
//    /**
//     * 统一线程工厂
//     */
//    private static class UnifiedThreadFactory implements ThreadFactory {
//        private final AtomicInteger threadNumber = new AtomicInteger(1);
//        private static final String NAME_PREFIX = "unified-priority-";
//
//        @Override
//        public Thread newThread(Runnable r) {
//            Thread thread = new Thread(r, NAME_PREFIX + threadNumber.getAndIncrement());
//            thread.setDaemon(false);
//            thread.setPriority(Thread.NORM_PRIORITY);
//            return thread;
//        }
//    }
//
//    /**
//     * 统一拒绝策略
//     */
//    private static class UnifiedRejectedExecutionHandler implements RejectedExecutionHandler {
//        @Override
//        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
//            if (r instanceof WrapPriorityTask) {
//                WrapPriorityTask task = (WrapPriorityTask) r;
//                PriorityMessage msg = task.getPriorityMessage();
//                log.warn("任务被拒绝: 消息类型={}, 优先级={}, 活跃线程={}, 队列大小={}",
//                        msg.getMessageType(), msg.getPriority(),
//                        executor.getActiveCount(), executor.getQueue().size());
//            }
//            throw new RejectedExecutionException("统一线程池队列已满，任务被拒绝执行");
//        }
//    }
//
//    /**
//     * 线程池统计信息
//     */
//    @Getter
//    public static class ThreadPoolStats {
//        private final int activeCount;
//        private final int corePoolSize;
//        private final int maximumPoolSize;
//        private final int poolSize;
//        private final int queueSize;
//        private final long completedTaskCount;
//        private final long taskCount;
//
//        public ThreadPoolStats(int activeCount, int corePoolSize, int maximumPoolSize,
//                             int poolSize, int queueSize, long completedTaskCount, long taskCount) {
//            this.activeCount = activeCount;
//            this.corePoolSize = corePoolSize;
//            this.maximumPoolSize = maximumPoolSize;
//            this.poolSize = poolSize;
//            this.queueSize = queueSize;
//            this.completedTaskCount = completedTaskCount;
//            this.taskCount = taskCount;
//        }
//    }
//}
