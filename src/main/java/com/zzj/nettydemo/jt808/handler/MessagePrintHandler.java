package com.zzj.nettydemo.jt808.handler;

import com.zzj.nettydemo.jt808.message.Message;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息打印处理器
 * 用于打印接收到的解析后的JT808消息内容
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
public class MessagePrintHandler extends ChannelInboundHandlerAdapter {

    private final String handlerName;

    public MessagePrintHandler(String handlerName) {
        this.handlerName = handlerName;
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        if (msg instanceof Message message) {

            log.info("=== {} ===", handlerName);
            log.info("消息类型: {}", String.format("0x%04X", message.getMessageType()));
            log.info("协议版本: {}", message.getVersion());
            log.info("终端手机号: {}", message.getHeader().getPhoneNumber());
            log.info("消息流水号: {}", message.getHeader().getFlowId());

//            if (log.isDebugEnabled()) {
                log.info("消息体详细信息: {}", message.getBody().toString());
//            }
        }

        // 传递给下一个处理器
        ctx.fireChannelRead(msg);

    }
}
