package com.zzj.nettydemo.jt808.handler.config;

import com.zzj.nettydemo.jt808.handler.config.strategy.RejectedStrategyFactory;
import com.zzj.nettydemo.jt808.handler.config.strategy.RejectionContext;
import com.zzj.nettydemo.jt808.handler.config.strategy.SubmissionRejectedStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 消息提交控制器
 * 控制不同消息类型提交到统一线程池的数量
 * <AUTHOR>
 * @date 2025-07-19
 */
@Slf4j
@Component
public class MessageSubmissionController {

    /**
     * 消息类型 -> 当前在线程池中的任务数量
     */
    private final ConcurrentHashMap<Integer, AtomicInteger> submittedCounts = new ConcurrentHashMap<>();

    /**
     * 消息类型 -> 最大提交数量限制
     */
    private final ConcurrentHashMap<Integer, Integer> maxSubmissionLimits = new ConcurrentHashMap<>();

    /**
     * 消息类型 -> 拒绝策略
     */
    private final ConcurrentHashMap<Integer, SubmissionRejectedStrategy> rejectedStrategies = new ConcurrentHashMap<>();

    /**
     * 设置消息类型的提交限制和拒绝策略
     */
    public void configureMessageType(MessageTypeConfig config) {
        int messageType = config.getMessageType();
        int maxSubmissions = config.getMaxConcurrentProcessing();
        String strategyName = config.getRejectedExecutionHandler();

        maxSubmissionLimits.put(messageType, maxSubmissions);
        rejectedStrategies.put(messageType, createRejectedStrategy(strategyName));
    }

    /**
     * 尝试获取提交许可（带上下文信息）
     */
    public boolean tryAcquireSubmission(int messageType, RejectionContext context) {
        AtomicInteger counter = submittedCounts.computeIfAbsent(messageType, k -> new AtomicInteger(0));
        Integer maxLimit = maxSubmissionLimits.get(messageType);

        if (maxLimit == null) {
            // 没有设置限制，允许无限提交
            counter.incrementAndGet();
            return true;
        }

        // 检查是否超过限制
        int current = counter.get();
        if (current >= maxLimit) {
            // 达到限制，执行拒绝策略
            SubmissionRejectedStrategy strategy = rejectedStrategies.getOrDefault(
                messageType, RejectedStrategyFactory.createStrategy("ABORT"));

            boolean result = strategy.handleRejection(messageType, current, maxLimit, context);

            if (result) {
                // 策略允许处理，增加计数
                counter.incrementAndGet();
            }

            return result;
        }

        // 原子性增加计数
        if (counter.incrementAndGet() <= maxLimit) {
            return true;
        }

        // 增加后超过限制，回退并执行拒绝策略
        counter.decrementAndGet();
        SubmissionRejectedStrategy strategy = rejectedStrategies.getOrDefault(
            messageType, RejectedStrategyFactory.createStrategy("ABORT"));

        boolean result = strategy.handleRejection(messageType, current, maxLimit, context);

        if (result) {
            // 策略允许处理，重新增加计数
            counter.incrementAndGet();
        }

        return result;
    }

    /**
     * 兼容性方法 - 不带上下文信息
     */
    public boolean tryAcquireSubmission(int messageType) {
        RejectionContext context = RejectionContext.builder()
            .rejectionTime(System.currentTimeMillis())
            .allowFallback(true)
            .maxRetryCount(3)
            .retryCount(0)
            .build();

        return tryAcquireSubmission(messageType, context);
    }

    /**
     * 释放提交许可（任务完成时调用）
     */
    public void releaseSubmission(int messageType) {
        AtomicInteger counter = submittedCounts.get(messageType);
        if (counter != null) {
            int current = counter.decrementAndGet();
            if (current < 0) {
                counter.set(0);
//                log.warn("消息类型 {} 的提交计数器出现负数，已重置",
//                        String.format("0x%04X", messageType));
            }
        }
    }

    /**
     * 创建拒绝策略 - 使用新的策略工厂
     */
    private SubmissionRejectedStrategy createRejectedStrategy(String strategyName) {
        return RejectedStrategyFactory.createStrategy(strategyName);
    }

    /**
     * 获取当前提交数量
     */
    public int getCurrentSubmissions(int messageType) {
        AtomicInteger counter = submittedCounts.get(messageType);
        return counter != null ? counter.get() : 0;
    }

    /**
     * 获取最大提交限制
     */
    public int getMaxSubmissionLimit(int messageType) {
        return maxSubmissionLimits.getOrDefault(messageType, Integer.MAX_VALUE);
    }

    /**
     * 记录统计信息
     */
    public void logStatistics() {
        log.info("=== 消息提交控制统计 ===");
        submittedCounts.forEach((messageType, counter) -> {
            int current = counter.get();
            int maxLimit = getMaxSubmissionLimit(messageType);
            String strategyName = rejectedStrategies.get(messageType) != null ?
                rejectedStrategies.get(messageType).getStrategyName() : "未配置";
            log.info("消息类型 {}: 当前提交={}, 最大限制={}, 拒绝策略={}",
                    String.format("0x%04X", messageType), current, maxLimit, strategyName);
        });
    }

    /**
     * 动态更新消息类型的拒绝策略
     */
    public void updateRejectedStrategy(int messageType, String strategyName) {
        SubmissionRejectedStrategy newStrategy = RejectedStrategyFactory.createStrategy(strategyName);
        rejectedStrategies.put(messageType, newStrategy);
        log.info("消息类型 {} 的拒绝策略已更新为: {}",
                String.format("0x%04X", messageType), newStrategy.getStrategyName());
    }
}
