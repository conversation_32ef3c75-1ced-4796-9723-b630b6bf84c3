package com.zzj.nettydemo.jt808.handler;

import cn.hutool.core.util.ClassUtil;
import com.zzj.nettydemo.jt808.protocol.Jt808Version;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025-07-16
 * 消息处理器工厂(使用反射动态加载处理器）
 */
public class MessageHandlerFactory {
    //版本-> 消息类型ID -> 处理器
    private static final Map<Jt808Version, Map<Integer, AbstractMessageHandler>> HANDLERS
            = new ConcurrentHashMap<>();

    static {
        // 扫描并注册所有带有@MessageType注解的处理器
        scanAndRegisterHandlers();
    }

    private  static void scanAndRegisterHandlers() {
        //使用ClassPathScanner扫描指定包下的所有类
        //检查每个类是否带有@MessageType注解
        //如果带有注解，则创建其实例并注册到HANDLERS中
        Set<Class<?>> handlers =
                ClassUtil.scanPackage("com.zzj.nettydemo.jt808.handler.impl");
        for (Class<?> handlerClass : handlers) {
            MessageType annotation = handlerClass.getAnnotation(MessageType.class);
            if(annotation == null){
                continue;
            }
            try {
                AbstractMessageHandler handler = (AbstractMessageHandler) handlerClass.
                        getDeclaredConstructor().newInstance();

                HANDLERS.computeIfAbsent(annotation.version(),
                                v -> new ConcurrentHashMap<>())
                        .put(annotation.messageType(), handler);

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static AbstractMessageHandler getHandler(Jt808Version version,int messageId) {
        Map<Integer, AbstractMessageHandler> handlerMap = HANDLERS.get(version);
        if(handlerMap == null){
            return null; // 返回null而不是抛异常，让调用方处理
        }
        return handlerMap.get(messageId); // 直接返回，可能为null
    }
}
