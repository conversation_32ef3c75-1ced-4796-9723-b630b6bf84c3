package com.zzj.nettydemo.jt808.handler.config.strategy;

import lombok.extern.slf4j.Slf4j;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 等待重试拒绝策略
 * 当达到限制时，等待一段时间后重试提交
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
public class WaitRetryRejectedStrategy implements SubmissionRejectedStrategy {

    private final long waitTimeMs;
    private final int maxRetryAttempts;
    private final AtomicLong totalRetryCount = new AtomicLong(0);

    public WaitRetryRejectedStrategy() {
        this(100, 3); // 默认等待100ms，最多重试3次
    }

    public WaitRetryRejectedStrategy(long waitTimeMs, int maxRetryAttempts) {
        this.waitTimeMs = waitTimeMs;
        this.maxRetryAttempts = maxRetryAttempts;
    }

    @Override
    public boolean handleRejection(int messageType, int currentCount, int maxLimit, RejectionContext context) {
        int retryCount = context.getRetryCount();

        if (retryCount >= maxRetryAttempts) {
            log.warn("消息类型 {} 重试次数已达上限 {}, 最终拒绝: 当前提交数={}, 最大限制={}",
                    String.format("0x%04X", messageType), maxRetryAttempts, currentCount, maxLimit);
            context.setRejectionReason("重试次数超限，最终拒绝");
            return false;
        }

        try {
            log.debug("消息类型 {} 第 {} 次重试，等待 {}ms: 当前提交数={}, 最大限制={}",
                    String.format("0x%04X", messageType), retryCount + 1, waitTimeMs, currentCount, maxLimit);

            // 等待一段时间
            Thread.sleep(waitTimeMs);

            // 增加重试次数
            context.setRetryCount(retryCount + 1);
            totalRetryCount.incrementAndGet();

            context.setRejectionReason("等待重试中");
            return true; // 允许重试

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("等待重试时被中断", e);
            context.setRejectionReason("等待重试被中断");
            return false;
        }
    }

    @Override
    public String getStrategyName() {
        return "WAIT_RETRY";
    }

    @Override
    public String getDescription() {
        return String.format("等待重试策略：等待%dms后重试，最多重试%d次", waitTimeMs, maxRetryAttempts);
    }

    /**
     * 获取总重试次数统计
     */
    public long getTotalRetryCount() {
        return totalRetryCount.get();
    }
}
