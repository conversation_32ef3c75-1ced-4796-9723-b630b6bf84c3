package com.zzj.nettydemo.jt808.handler.config.strategy;

import lombok.extern.slf4j.Slf4j;

/**
 * 中止拒绝策略
 * 直接拒绝任务，不进行任何处理
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
public class AbortRejectedStrategy implements SubmissionRejectedStrategy {

    @Override
    public boolean handleRejection(int messageType, int currentCount, int maxLimit, RejectionContext context) {
        log.warn("消息类型 {} 提交被拒绝(中止策略): 当前提交数={}, 最大限制={}, 终端={}",
                String.format("0x%04X", messageType),
                currentCount,
                maxLimit,
                context.getOriginalMessage() != null ? context.getOriginalMessage().getHeader().getPhoneNumber() : "未知");

        // 更新拒绝原因
        context.setRejectionReason("达到并发处理限制，直接拒绝");

        // 可以在这里发送拒绝响应给终端
        sendRejectionResponse(context);

        return false; // 完全拒绝
    }

    @Override
    public String getStrategyName() {
        return "ABORT";
    }

    @Override
    public String getDescription() {
        return "中止策略：直接拒绝超出限制的任务";
    }

    /**
     * 发送拒绝响应给终端
     */
    private void sendRejectionResponse(RejectionContext context) {
        if (context.getChannelContext() != null && context.getChannelContext().channel().isActive()) {
            // 这里可以构造一个通用应答消息，告知终端服务器繁忙
            log.debug("向终端发送服务器繁忙响应");
            // 具体的响应消息构造可以根据JT808协议实现
        }
    }
}
