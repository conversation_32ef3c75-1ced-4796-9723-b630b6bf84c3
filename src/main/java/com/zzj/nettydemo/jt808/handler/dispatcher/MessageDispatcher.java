package com.zzj.nettydemo.jt808.handler.dispatcher;

import com.zzj.nettydemo.jt808.handler.AbstractMessageHandler;
import com.zzj.nettydemo.jt808.handler.MessageHandlerFactory;
import com.zzj.nettydemo.jt808.handler.config.MessageConfigurationManager;
import com.zzj.nettydemo.jt808.handler.config.MessageTypeConfig;
import com.zzj.nettydemo.jt808.handler.config.MessageSubmissionController;
import com.zzj.nettydemo.jt808.handler.config.strategy.RejectionContext;
import com.zzj.nettydemo.jt808.handler.message.PriorityMessage;
import com.zzj.nettydemo.jt808.message.Message;
import com.zzj.nettydemo.jt808.message.response.TerminalRegisterResponseBuilder;
import com.zzj.nettydemo.jt808.message.response.PlatformCommonResponseBuilder;
import com.zzj.nettydemo.jt808.message.body.v19.V2019PlatformCommonResponseMessageBody;
import com.zzj.nettydemo.jt808.message.body.v19.V2019TerminalAuthMessageBody;
import com.zzj.nettydemo.jt808.message.body.v19.V2019LocationBatchUploadMessageBody;
import com.zzj.nettydemo.jt808.message.body.v19.V2019LocationReportMessageBody;
import com.zzj.nettydemo.service.LocationCacheService;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.List;
import java.util.ArrayList;

/**
 * 消息分发器 - 统一优先级线程池架构
 * <AUTHOR>
 * @date 2025-07-19
 */
@Slf4j
@Component
@ChannelHandler.Sharable
public class MessageDispatcher extends ChannelInboundHandlerAdapter {

//    @Resource
//    private MessagePriorityThreadPool unifiedThreadPool;

    @Resource
    private MessageSubmissionController submissionController;

    @Resource
    private MessageConfigurationManager configManager;

    @Resource
    private LocationCacheService locationCacheService;


    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        if (!(msg instanceof Message)) {
            log.warn("接收到非 Message 类型的消息: {}", msg.getClass().getSimpleName());
            ctx.fireChannelRead(msg);
            return;
        }

        Message message = (Message) msg;

        // 快速测试：直接处理0x0100终端注册消息
        if (message.getMessageType() == 0x0100) {
            handleTerminalRegisterQuickTest(ctx, message);
            return;
        }

        // 特判处理：直接处理0x0102终端鉴权消息
        if (message.getMessageType() == 0x0102) {
            handleTerminalAuthQuickTest(ctx, message);
            return;
        }

        // 特判处理：直接处理0x0706定位数据批量上传消息
        if(message.getMessageType() == 0x0704){
            handleLocationBatchUploadMessage(ctx, message);
            return;
        }

        // 特判处理：直接处理0x0200位置信息汇报
        if(message.getMessageType() == 0x0200){
            handleLocationReportMessage(ctx, message);
            return;
        }

        log.error("接收到未知消息类型: 0x{}", String.format("%04X", message.getMessageType()));

        // 异步分发消息处理
//        dispatch(message, ctx)
//            .whenComplete((result, throwable) -> {
//                if (throwable != null) {
//                    log.error("消息处理失败: {}", throwable.getMessage());
//                    // 可以在这里发送错误响应
//                    handleProcessingError(ctx, message, throwable);
//                } else {
//                    log.debug("消息处理成功完成");
//                }
//            });
    }

    /**
     * 处理消息处理过程中的错误
     */
    private void handleProcessingError(ChannelHandlerContext ctx, Message message, Throwable throwable) {
        try {
            // 这里可以构造错误响应消息并发送
            log.error("处理消息ID: {} 时发生错误", String.format("0x%04X", message.getMessageType()), throwable);

            // 如果需要，可以在这里发送通用应答等错误响应
            // ctx.writeAndFlush(errorResponse);

        } catch (Exception e) {
            log.error("发送错误响应时发生异常", e);
        }
    }

    /**
     * 快速测试：处理0x0100终端注册消息
     * 直接返回0x8100注册成功应答
     */
    private void handleTerminalRegisterQuickTest(ChannelHandlerContext ctx, Message message) {
        try {
            log.info("=== 快速测试：处理终端注册消息 0x0100 ===");
            log.info("接收到终端注册请求，消息流水号: {}", message.getHeader().getFlowId());

            // 使用专门的构建器构造0x8100终端注册应答消息（原始消息体+消息头）
            byte[] responseMessage = TerminalRegisterResponseBuilder.buildSuccessResponse(message);

            log.info("构建的原始应答消息长度: {} 字节", responseMessage.length);
            log.info("原始应答消息内容: {}", bytesToHex(responseMessage));

            // 重要：响应消息需要通过JT808Encoder进行编码
            // 这将添加7E标识位、计算校验码、进行转义处理等
            log.info("准备发送响应消息，将经过JT808Encoder编码处理");
            ctx.writeAndFlush(responseMessage);

            log.info("已发送终端注册应答 0x8100，注册成功，消息已提交到编码器");

        } catch (Exception e) {
            log.error("处理终端注册消息失败", e);
        }
    }

    /**
     * 快速测试：处理0x0102终端鉴权消息
     * 进行简单鉴权（鉴权码和消息头手机号一致即为正确），返回平台通用应答消息
     */
    private void handleTerminalAuthQuickTest(ChannelHandlerContext ctx, Message message) {
        try {
            log.info("=== 快速测试：处理终端鉴权消息 0x0102 ===");
            log.info("接收到终端鉴权请求，消息流水号: {}", message.getHeader().getFlowId());
            log.info("终端手机号: {}", message.getHeader().getPhoneNumber());

            // 解码鉴权消息体
            V2019TerminalAuthMessageBody authBody = new V2019TerminalAuthMessageBody();
            authBody.decode(message.getBody().encode());

            log.info("鉴权码: {}", authBody.getAuthCode());
            log.info("终端IMEI: {}", authBody.getImeiAsString());
            log.info("软件版本: {}", authBody.getSoftwareVersionAsString());

            // 简单鉴权逻辑：鉴权码和消息头手机号一致即为正确
            String terminalPhone = message.getHeader().getPhoneNumber();
            String authCode = authBody.getAuthCode();

            V2019PlatformCommonResponseMessageBody.ResponseResult result;
            if (authCode != null && authCode.equals(terminalPhone)) {
                result = V2019PlatformCommonResponseMessageBody.ResponseResult.SUCCESS;
                log.info("鉴权成功：鉴权码({}) 与手机号({}) 匹配", authCode, terminalPhone);
            } else {
                result = V2019PlatformCommonResponseMessageBody.ResponseResult.FAILURE;
                log.warn("鉴权失败：鉴权码({}) 与手机号({}) 不匹配", authCode, terminalPhone);
            }

            // 构建平台通用应答消息
            byte[] responseMessage = PlatformCommonResponseBuilder.buildResponse(message, result);

            log.info("构建的平台通用应答消息长度: {} 字节", responseMessage.length);
            log.info("平台通用应答消息内容: {}", bytesToHex(responseMessage));
            log.info("应答结果: {}", result.getDescription());

            // 发送响应消息
            ctx.writeAndFlush(responseMessage);

            log.info("已发送平台通用应答 0x8001，鉴权{}，消息已提交到编码器",
                    result == V2019PlatformCommonResponseMessageBody.ResponseResult.SUCCESS ? "成功" : "失败");

        } catch (Exception e) {
            log.error("处理终端鉴权消息失败", e);
            try {
                // 发送消息有误的应答
                byte[] errorResponse = PlatformCommonResponseBuilder.buildMessageErrorResponse(message);
                ctx.writeAndFlush(errorResponse);
                log.info("已发送消息有误应答");
            } catch (Exception ex) {
                log.error("发送错误应答失败", ex);
            }
        }
    }

    /**
     * 特判处理：处理0x0704 定位数据批量上传消息
     */
    private void handleLocationBatchUploadMessage(ChannelHandlerContext ctx, Message message) {
        try {
            log.info("=== 特判处理：处理定位数据批量上传消息 0x0704 ===");
            log.info("接收到定位数据批量上传请求，消息流水号: {}", message.getHeader().getFlowId());

            String phoneNumber = message.getHeader().getPhoneNumber();
            log.info("终端手机号: {}", phoneNumber);

            // 解码定位数据批量上传消息体
            V2019LocationBatchUploadMessageBody batchUploadBody = new V2019LocationBatchUploadMessageBody();
            batchUploadBody.decode(message.getBody().encode());

            log.info("=== 定位数据批量上传详细信息 ===");
            log.info("数据项个数: {}", batchUploadBody.getItemCount());
            log.info("位置数据类型: {}", batchUploadBody.getLocationTypeDescription());

            // 解析所有位置汇报数据并保存到缓存
            List<V2019LocationReportMessageBody> locationReports = new ArrayList<>();

            // 输出每一条位置汇报数据项的基本信息
            if (batchUploadBody.getLocationDataItems() != null) {
                for (int i = 0; i < batchUploadBody.getLocationDataItems().size(); i++) {
                    V2019LocationBatchUploadMessageBody.LocationReportDataItem item =
                        batchUploadBody.getLocationDataItems().get(i);

                    log.info("--- 位置数据项 {} ---", i + 1);
                    log.info("数据体长度: {} 字节", item.getDataLength());

                    // 尝试解析位置汇报数据
                    try {
                        V2019LocationReportMessageBody locationReport = item.parseToLocationReport();
                        if (locationReport != null) {
                            locationReports.add(locationReport);

                            // 输出位置基本信息
                            log.info("位置基本信息:");
                            log.info("  报警标志: 0x{}", String.format("%08X", locationReport.getBasicInfo().getAlarmFlag()));
                            log.info("  状态: 0x{}", String.format("%08X", locationReport.getBasicInfo().getStatus()));
                            log.info("  纬度: {}", locationReport.getBasicInfo().getLatitude() / 1000000.0);
                            log.info("  经度: {}", locationReport.getBasicInfo().getLongitude() / 1000000.0);
                            log.info("  高程: {} 米", locationReport.getBasicInfo().getAltitude());
                            log.info("  速度: {} km/h", locationReport.getBasicInfo().getSpeed() / 10.0);
                            log.info("  方向: {} 度", locationReport.getBasicInfo().getDirection());
                            log.info("  时间: {}", locationReport.getBasicInfo().getTimeAsDateTime());

                            // 输出位置附加信息
                            if (locationReport.getAdditionalInfoList() != null &&
                                !locationReport.getAdditionalInfoList().isEmpty()) {
                                log.info("位置附加信息:");
                                for (V2019LocationReportMessageBody.LocationAdditionalInfo addinfo : locationReport.getAdditionalInfoList()) {
                                    log.info("  附加信息ID: 0x{}, 长度: {} 字节",
                                            String.format("%02X", addinfo.getInfoId()),
                                            addinfo.getInfoLength());
                                }
                            } else {
                                log.info("位置附加信息: 无");
                            }
                        } else {
                            log.warn("位置数据项 {} 解析失败", i + 1);
                        }
                    } catch (Exception e) {
                        log.error("解析位置数据项 {} 时发生错误: {}", i + 1, e.getMessage());
                        log.info("原始数据长度: {} 字节", item.getDataBody() != null ? item.getDataBody().length : 0);
                    }

                    log.info(""); // 空行分隔
                }
            }

            // 批量保存位置信息到缓存
            if (!locationReports.isEmpty()) {
                try {
                    locationCacheService.addLocationInfoBatch(phoneNumber, locationReports);
                    log.info("批量位置信息已保存到缓存，终端: {}, 数量: {}", phoneNumber, locationReports.size());
                } catch (Exception e) {
                    log.error("批量保存位置信息到缓存时发生错误", e);
                }
            }

            // 构建平台通用应答消息
            byte[] responseMessage = PlatformCommonResponseBuilder.buildResponse(
                message, V2019PlatformCommonResponseMessageBody.ResponseResult.SUCCESS);

            log.info("构建的平台通用应答消息长度: {} 字节", responseMessage.length);
            log.info("平台通用应答消息内容: {}", bytesToHex(responseMessage));

            // 发送响应消息
            ctx.writeAndFlush(responseMessage);

            log.info("已发送平台通用应答 0x8001，定位数据批量上传处理成功，消息已提交到编码器");

        } catch (Exception e) {
            log.error("处理定位数据批量上传消息失败", e);
            try {
                // 发送消息有误的应答
                byte[] errorResponse = PlatformCommonResponseBuilder.buildMessageErrorResponse(message);
                ctx.writeAndFlush(errorResponse);
                log.info("已发送消息有误应答");
            } catch (Exception ex) {
                log.error("发送错误应答失败", ex);
            }
        }
    }

    /**
     * 特判处理：处理0x0200 位置信息汇报消息
     */
    private void handleLocationReportMessage(ChannelHandlerContext ctx, Message message) {
        try {
            log.info("=== 特判处理：处理位置信息汇报消息 0x0200 ===");
            log.info("接收到位置信息汇报请求，消息流水号: {}", message.getHeader().getFlowId());
            log.info("终端手机号: {}", message.getHeader().getPhoneNumber());

            // 解码位置信息汇报消息体
            V2019LocationReportMessageBody locationBody = new V2019LocationReportMessageBody();
            locationBody.decode(message.getBody().encode());

            log.info("=== 位置信息汇报详细信息 ===");

            // 输出位置基本信息
            if (locationBody.getBasicInfo() != null) {
                V2019LocationReportMessageBody.LocationBasicInfo basicInfo = locationBody.getBasicInfo();

                log.info("位置基本信息:");
                log.info("  报警标志: 0x{}", String.format("%08X", basicInfo.getAlarmFlag()));
                log.info("  状态标志: 0x{}", String.format("%08X", basicInfo.getStatus()));

                // 解析报警标志
                parseAlarmFlags(basicInfo.getAlarmFlag());

                // 解析状态标志
                parseStatusFlags(basicInfo.getStatus());

                log.info("  纬度: {} (原始值: {})", basicInfo.getLatitude() / 1000000.0, basicInfo.getLatitude());
                log.info("  经度: {} (原始值: {})", basicInfo.getLongitude() / 1000000.0, basicInfo.getLongitude());
                log.info("  高程: {} 米", basicInfo.getAltitude());
                log.info("  速度: {} km/h (原始值: {})", basicInfo.getSpeed() / 10.0, basicInfo.getSpeed());
                log.info("  方向: {} 度", basicInfo.getDirection());
                log.info("  时间: {}", basicInfo.getTimeAsDateTime());
            }

            // 输出位置附加信息
            if (locationBody.getAdditionalInfoList() != null && !locationBody.getAdditionalInfoList().isEmpty()) {
                log.info("位置附加信息 (共{}项):", locationBody.getAdditionalInfoList().size());

                for (int i = 0; i < locationBody.getAdditionalInfoList().size(); i++) {
                    V2019LocationReportMessageBody.LocationAdditionalInfo addInfo =
                        locationBody.getAdditionalInfoList().get(i);

                    log.info("  附加信息 {}: ID=0x{}, 长度={} 字节",
                           i + 1,
                           String.format("%02X", addInfo.getInfoId()),
                           addInfo.getInfoLength());

                    // 解析常见的附加信息类型
                    parseAdditionalInfo(addInfo);
                }
            } else {
                log.info("位置附加信息: 无");
            }

            // 保存位置信息到缓存
            try {
                String phoneNumber = message.getHeader().getPhoneNumber();
                locationCacheService.addLocationInfo(phoneNumber, locationBody);
                log.info("位置信息已保存到缓存，终端: {}", phoneNumber);
            } catch (Exception e) {
                log.error("保存位置信息到缓存时发生错误", e);
            }

            // 构建平台通用应答消息
            byte[] responseMessage = PlatformCommonResponseBuilder.buildResponse(
                message, V2019PlatformCommonResponseMessageBody.ResponseResult.SUCCESS);

            log.info("构建的平台通用应答消息长度: {} 字节", responseMessage.length);
            log.info("平台通用应答消息内容: {}", bytesToHex(responseMessage));

            // 发送响应消息
            ctx.writeAndFlush(responseMessage);

            log.info("已发送平台通用应答 0x8001，位置信息汇报处理成功，消息已提交到编码器");

        } catch (Exception e) {
            log.error("处理位置信息汇报消息失败", e);
            try {
                // 发送消息有误的应答
                byte[] errorResponse = PlatformCommonResponseBuilder.buildMessageErrorResponse(message);
                ctx.writeAndFlush(errorResponse);
                log.info("已发送消息有误应答");
            } catch (Exception ex) {
                log.error("发送错误应答失败", ex);
            }
        }
    }

    /**
     * 解析报警标志位
     */
    private void parseAlarmFlags(int alarmFlag) {
        if (alarmFlag == 0) {
            log.info("    无报警");
            return;
        }

        log.info("    报警详情:");
        if ((alarmFlag & 0x00000001) != 0) log.info("      - 紧急报警");
        if ((alarmFlag & 0x00000002) != 0) log.info("      - 超速报警");
        if ((alarmFlag & 0x00000004) != 0) log.info("      - 疲劳驾驶");
        if ((alarmFlag & 0x00000008) != 0) log.info("      - 危险预警");
        if ((alarmFlag & 0x00000010) != 0) log.info("      - GNSS模块发生故障");
        if ((alarmFlag & 0x00000020) != 0) log.info("      - GNSS天线未接或被剪断");
        if ((alarmFlag & 0x00000040) != 0) log.info("      - GNSS天线短路");
        if ((alarmFlag & 0x00000080) != 0) log.info("      - 终端主电源欠压");
        if ((alarmFlag & 0x00000100) != 0) log.info("      - 终端主电源掉电");
        if ((alarmFlag & 0x00000200) != 0) log.info("      - 终端LCD或显示器故障");
        if ((alarmFlag & 0x00000400) != 0) log.info("      - TTS模块故障");
        if ((alarmFlag & 0x00000800) != 0) log.info("      - 摄像头故障");
        if ((alarmFlag & 0x00001000) != 0) log.info("      - 道路运输证IC卡模块故障");
        if ((alarmFlag & 0x00002000) != 0) log.info("      - 超速预警");
        if ((alarmFlag & 0x00004000) != 0) log.info("      - 疲劳驾驶预警");
        if ((alarmFlag & 0x00008000) != 0) log.info("      - 违规行驶");
        if ((alarmFlag & 0x00010000) != 0) log.info("      - 轮胎气压异常");
        if ((alarmFlag & 0x00020000) != 0) log.info("      - 右转盲区异常");
        // 可以继续添加更多报警位的解析
    }

    /**
     * 解析状态标志位
     */
    private void parseStatusFlags(int status) {
        log.info("    状态详情:");
        log.info("      - ACC: {}", (status & 0x00000001) != 0 ? "开" : "关");
        log.info("      - 定位: {}", (status & 0x00000002) != 0 ? "有效" : "无效");
        log.info("      - 纬度半球: {}", (status & 0x00000004) != 0 ? "南纬" : "北纬");
        log.info("      - 经度半球: {}", (status & 0x00000008) != 0 ? "西经" : "东经");
        log.info("      - 运营状态: {}", (status & 0x00000010) != 0 ? "运营" : "停运");
        log.info("      - 经纬度加密: {}", (status & 0x00000020) != 0 ? "已加密" : "未加密");

        // 解析载重状态 (第6-7位)
        int loadStatus = (status >> 6) & 0x03;
        String loadStr = switch (loadStatus) {
            case 0 -> "空车";
            case 1 -> "半载";
            case 2 -> "保留";
            case 3 -> "满载";
            default -> "未知";
        };
        log.info("      - 载重状态: {}", loadStr);

        log.info("      - 车辆油路: {}", (status & 0x00000400) != 0 ? "断开" : "正常");
        log.info("      - 车辆电路: {}", (status & 0x00000800) != 0 ? "断开" : "正常");
        log.info("      - 车门: {}", (status & 0x00001000) != 0 ? "锁定" : "解锁");
        log.info("      - 门1: {}", (status & 0x00002000) != 0 ? "开" : "关");
        log.info("      - 门2: {}", (status & 0x00004000) != 0 ? "开" : "关");
        log.info("      - 门3: {}", (status & 0x00008000) != 0 ? "开" : "关");
        log.info("      - 门4: {}", (status & 0x00010000) != 0 ? "开" : "关");
        log.info("      - 门5: {}", (status & 0x00020000) != 0 ? "开" : "关");
        log.info("      - GPS定位: {}", (status & 0x00040000) != 0 ? "使用" : "未使用");
        log.info("      - 北斗定位: {}", (status & 0x00080000) != 0 ? "使用" : "未使用");
        log.info("      - GLONASS定位: {}", (status & 0x00100000) != 0 ? "使用" : "未使用");
        log.info("      - Galileo定位: {}", (status & 0x00200000) != 0 ? "使用" : "未使用");
    }

    /**
     * 解析附加信息
     */
    private void parseAdditionalInfo(V2019LocationReportMessageBody.LocationAdditionalInfo addInfo) {
        int infoId = addInfo.getInfoId() & 0xFF;
        byte[] content = addInfo.getInfoContent();

        switch (infoId) {
            case 0x01: // 里程
                if (content != null && content.length >= 4) {
                    int mileage = ((content[0] & 0xFF) << 24) |
                                 ((content[1] & 0xFF) << 16) |
                                 ((content[2] & 0xFF) << 8) |
                                 (content[3] & 0xFF);
                    log.info("      内容: 里程 {} km", mileage / 10.0);
                }
                break;
            case 0x02: // 油量
                if (content != null && content.length >= 2) {
                    int fuel = ((content[0] & 0xFF) << 8) | (content[1] & 0xFF);
                    log.info("      内容: 油量 {} L", fuel / 10.0);
                }
                break;
            case 0x03: // 行驶记录功能获取的速度
                if (content != null && content.length >= 2) {
                    int speed = ((content[0] & 0xFF) << 8) | (content[1] & 0xFF);
                    log.info("      内容: 行驶记录速度 {} km/h", speed / 10.0);
                }
                break;
            case 0x04: // 需要人工确认报警事件的ID
                if (content != null && content.length >= 2) {
                    int alarmId = ((content[0] & 0xFF) << 8) | (content[1] & 0xFF);
                    log.info("      内容: 需人工确认的报警事件ID {}", alarmId);
                }
                break;
            case 0x11: // 超速报警附加信息
                if (content != null && content.length >= 1) {
                    int overspeedType = content[0] & 0xFF;
                    String typeStr = (overspeedType == 0) ? "无特定位置" :
                                   (overspeedType == 1) ? "圆形区域" :
                                   (overspeedType == 2) ? "矩形区域" :
                                   (overspeedType == 3) ? "多边形区域" :
                                   (overspeedType == 4) ? "路段" : "未知";
                    log.info("      内容: 超速报警类型 - {}", typeStr);

                    if (content.length >= 5) {
                        int areaId = ((content[1] & 0xFF) << 24) |
                                   ((content[2] & 0xFF) << 16) |
                                   ((content[3] & 0xFF) << 8) |
                                   (content[4] & 0xFF);
                        log.info("      区域或路段ID: {}", areaId);
                    }
                }
                break;
            case 0x12: // 进出区域/路线报警附加信息
                if (content != null && content.length >= 6) {
                    int locationType = content[0] & 0xFF;
                    int areaId = ((content[1] & 0xFF) << 24) |
                               ((content[2] & 0xFF) << 16) |
                               ((content[3] & 0xFF) << 8) |
                               (content[4] & 0xFF);
                    int direction = content[5] & 0xFF;

                    String locationStr = switch (locationType) {
                        case 1 -> "圆形区域";
                        case 2 -> "矩形区域";
                        case 3 -> "多边形区域";
                        case 4 -> "路段";
                        default -> "未知";
                    };

                    String directionStr = (direction == 0) ? "进入" : "离开";
                    log.info("      内容: {} {} (ID: {})", directionStr, locationStr, areaId);
                }
                break;
            case 0x25: // 扩展车辆信号状态位
                if (content != null && content.length >= 4) {
                    int signalStatus = ((content[0] & 0xFF) << 24) |
                                     ((content[1] & 0xFF) << 16) |
                                     ((content[2] & 0xFF) << 8) |
                                     (content[3] & 0xFF);
                    log.info("      内容: 扩展车辆信号状态 0x{}", String.format("%08X", signalStatus));

                    // 可以进一步解析具体的信号状态位
                    log.info("        - 近光灯: {}", (signalStatus & 0x00000001) != 0 ? "开" : "关");
                    log.info("        - 远光灯: {}", (signalStatus & 0x00000002) != 0 ? "开" : "关");
                    log.info("        - 右转灯: {}", (signalStatus & 0x00000004) != 0 ? "开" : "关");
                    log.info("        - 左转灯: {}", (signalStatus & 0x00000008) != 0 ? "开" : "关");
                    log.info("        - 制动: {}", (signalStatus & 0x00000010) != 0 ? "制动" : "正常");
                    log.info("        - 倒车: {}", (signalStatus & 0x00000020) != 0 ? "倒车" : "正常");
                    log.info("        - 雾灯: {}", (signalStatus & 0x00000040) != 0 ? "开" : "关");
                    log.info("        - 示廓灯: {}", (signalStatus & 0x00000080) != 0 ? "开" : "关");
                }
                break;
            case 0x30: // 无线通信网络信号强度
                if (content != null && content.length >= 1) {
                    int signalStrength = content[0] & 0xFF;
                    log.info("      内容: 无线通信网络信号强度 {}%", signalStrength);
                }
                break;
            case 0x31: // GNSS定位卫星数
                if (content != null && content.length >= 1) {
                    int satelliteCount = content[0] & 0xFF;
                    log.info("      内容: GNSS定位卫星数 {} 颗", satelliteCount);
                }
                break;
            default:
                log.info("      内容: 未解析的附加信息 (原始数据: {})",
                       content != null ? bytesToHex(content) : "null");
                break;
        }
    }

    /**
     * 字节数组转十六进制字符串（用于调试）
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02X ", b));
        }
        return result.toString().trim();
    }

    /**
     * 分发消息进行异步处理
     */
//    public CompletableFuture<Void> dispatch(Message message, ChannelHandlerContext ctx) {
//        int messageType = message.getMessageType();
//
//        try {
//            // 1. 获取消息处理器
//            AbstractMessageHandler handler = MessageHandlerFactory.getHandler(message.getVersion(), messageType);
//            if (handler == null) {
//                log.warn("未找到消息类型 {} 的处理器", String.format("0x%04X", messageType));
//                return CompletableFuture.completedFuture(null);
//            }
//
//            // 2. 创建拒绝上下文
//            RejectionContext rejectionContext = RejectionContext.builder()
//                .originalMessage(message)
//                .channelContext(ctx)
//                .threadPool(unifiedThreadPool)
//                .rejectionTime(System.currentTimeMillis())
//                .allowFallback(true)
//                .maxRetryCount(3)
//                .retryCount(0)
//                .build();
//
//            // 3. 检查提交限制（使用新的拒绝策略）
//            if (!submissionController.tryAcquireSubmission(messageType, rejectionContext)) {
//                log.warn("消息类型 {} 提交被拒绝：{}",
//                        String.format("0x%04X", messageType),
//                        rejectionContext.getRejectionReason());
//                return CompletableFuture.failedFuture(
//                    new RuntimeException("消息类型 " + String.format("0x%04X", messageType) +
//                                       " 提交被拒绝: " + rejectionContext.getRejectionReason()));
//            }
//
//            // 4. 获取配置并创建优先级消息
//            MessageTypeConfig config = configManager.getConfig(messageType);
//            if(config==null){
//                log.warn("消息类型 {} 未配置,拒绝执行", String.format("0x%04X", messageType));
//                submissionController.releaseSubmission(messageType);
//                return CompletableFuture.failedFuture(
//                    new RuntimeException("消息类型 " + String.format("0x%04X", messageType) + " 未配置"));
//            }
//            int priority = config.getPriority();
//            PriorityMessage priorityMessage = new PriorityMessage(message, priority, messageType);
//
//            // 5. 创建处理任务
//            Runnable processingTask = () -> {
//                long startTime = System.currentTimeMillis();
//                try {
//                    log.debug("开始处理消息类型: {}, 优先级: {}",
//                            String.format("0x%04X", messageType), priority);
//
//                    // 执行具体的消息处理逻辑
//                    if (ctx != null) {
//                        handler.handleAsync(message, ctx).join();
//                    } else {
//                        handler.handle(message);
//                    }
//
//                    long processingTime = System.currentTimeMillis() - startTime;
//                    log.debug("消��类型 {} 处理完成，耗时: {}ms",
//                            String.format("0x%04X", messageType), processingTime);
//
//                } catch (Exception e) {
//                    log.error("处理消息类型 {} 时发生错误", String.format("0x%04X", messageType), e);
//                    throw new RuntimeException("消息处理失败", e);
//                } finally {
//                    // 释放提交计数
//                    submissionController.releaseSubmission(messageType);
//                }
//            };
//
//            // 6. 提交到统一优先级线程池
//            return unifiedThreadPool.submit(priorityMessage, processingTask)
//                    .exceptionally(throwable -> {
//                        log.error("消息类型 {} 异步处理失败", String.format("0x%04X", messageType), throwable);
//                        submissionController.releaseSubmission(messageType);
//                        return null;
//                    });
//
//        } catch (Exception e) {
//            log.error("分发消息类型 {} 时发生错误", String.format("0x%04X", messageType), e);
//            return CompletableFuture.failedFuture(e);
//        }
//    }
}
