package com.zzj.nettydemo.jt808.handler.config.strategy;

import lombok.extern.slf4j.Slf4j;

/**
 * 丢弃最老任务拒绝策略
 * 当达到限制时，尝试从队列中移除最老的任务，为新任务腾出空间
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
public class DiscardOldestRejectedStrategy implements SubmissionRejectedStrategy {

    @Override
    public boolean handleRejection(int messageType, int currentCount, int maxLimit, RejectionContext context) {
        log.info("消息类型 {} 达到限制，尝试丢弃最老任务: 当前提交数={}, 最大限制={}, 终端={}",
                String.format("0x%04X", messageType),
                currentCount,
                maxLimit,
                context.getOriginalMessage() != null ? context.getOriginalMessage().getHeader().getPhoneNumber() : "未知");

        // 尝试从线程池队列中移除最老的同类型任务
        boolean discarded = tryDiscardOldestTask(messageType, context);

        if (discarded) {
            log.info("成功丢弃消息类型 {} 的最老任务，新任务将被处理", String.format("0x%04X", messageType));
            context.setRejectionReason("丢弃最老任务，接受新任务");
            return true; // 允许新任务执行
        } else {
            log.warn("无法丢弃消息类型 {} 的最老任务，新任务被拒绝", String.format("0x%04X", messageType));
            context.setRejectionReason("无法丢弃最老任务，拒绝新任务");
            return false; // 拒绝新任务
        }
    }

    /**
     * 尝试丢弃最老的同类型任务
     */
    private boolean tryDiscardOldestTask(int messageType, RejectionContext context) {
        return true;
//        if (context.getThreadPool() == null) {
//            return false;
//        }
//
//        try {
//            // 这里需要线程池提供相应的方法来移除最老的特定类型任务
//            // 由于线程池的实现复杂性，这里只是记录日志，实际实现需要配合线程池的具体实现
//            log.debug("尝试从队列中移除消息类型 {} 的最老任务", String.format("0x%04X", messageType));
//
//            // TODO: 实现实际的移除逻辑
//            // return context.getThreadPool().removeOldestTask(messageType);
//
//            return false; // 暂时返回false，待实现具体逻辑
//        } catch (Exception e) {
//            log.error("丢弃最老任务时发生异常", e);
//            return false;
//        }
    }

    @Override
    public String getStrategyName() {
        return "DISCARD_OLDEST";
    }

    @Override
    public String getDescription() {
        return "丢弃最老策略：移除队列中最老的同类型任务，为新任务腾出空间";
    }
}
