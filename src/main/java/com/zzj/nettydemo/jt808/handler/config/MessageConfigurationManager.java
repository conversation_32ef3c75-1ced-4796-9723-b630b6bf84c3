package com.zzj.nettydemo.jt808.handler.config;

import cn.hutool.core.collection.CollectionUtil;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 消息配置管理器 - 统一线程池架构
 * 从 message-config.yml 配置文件中读取配置信息
 * <AUTHOR>
 * @date 2025-07-19
 */
@Slf4j
@Component
@ConfigurationProperties
@PropertySource(value = "classpath:message-config.yml")
public class MessageConfigurationManager {

    @Resource
    private MessageSubmissionController submissionController;
    /**
     * 消息类型配置列表（从配置文件加载）
     */
    @Setter
    @Getter
    private List<MessageTypeConfig> types;

    /**
     * 消息版本_消息类型 -> 配置映射
     */
    private final Map<Integer, MessageTypeConfig> configMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        loadConfigurations();
        setupSubmissionController();
        log.info("消息配置管理器初始化完成，共加载 {} 个消息类型配置", configMap.size());
    }

    /**
     * 加载配置
     */
    private void loadConfigurations() {
        if (CollectionUtil.isNotEmpty(types)) {
            for (MessageTypeConfig config : types) {
                if (!config.isEnabled()) {
//                    log.debug("消息类型 {} 已禁用，跳过加载", String.format("0x%04X", config.getMessageType()));
                    continue;
                }
                configMap.put(config.getMessageType(), config);
//                log.debug("成功加载消息类型配置: {}", formatConfigInfo(config));
            }
            log.info("配置加载完成，共加载 {} 个有效配置项", configMap.size());
        } else {
            log.warn("未从 message-config.yml 中找到任何消息类型配置，将使用默认配置");
        }
    }

    /**
     * 设置提交控制器
     */
    private void setupSubmissionController() {
        configMap.values().forEach(submissionController::configureMessageType);
    }

    /**
     * 获取指定消息类型的配置
     */
    public MessageTypeConfig getConfig(int messageType) {
        return configMap.get(messageType);
    }

    /**
     * 获取所有配置
     */
    public Map<Integer, MessageTypeConfig> getAllConfigs() {
        return new HashMap<>(configMap);
    }

    /*
     * 动态添加配置
     */
//    public void addConfig(MessageTypeConfig config) {
//        configMap.put(config.getMessageType(), config);
//        submissionController.configureMessageType(config);
//        log.info("动态添加消息类型 {} 配置", String.format("0x%04X", config.getMessageType()));
//    }

    /*
     * 更新配置
     */
//    public void updateConfig(int messageType, MessageTypeConfig newConfig) {
//        if (configMap.containsKey(messageType)) {
//            configMap.put(messageType, newConfig);
//            submissionController.configureMessageType(newConfig);
//            log.info("更新消息类型 {} 配置", String.format("0x%04X", messageType));
//        } else {
//            log.warn("消息类型 {} 不存在，无法更新配置", String.format("0x%04X", messageType));
//        }
//    }
}
