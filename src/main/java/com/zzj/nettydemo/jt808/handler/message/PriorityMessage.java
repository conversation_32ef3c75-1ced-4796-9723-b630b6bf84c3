package com.zzj.nettydemo.jt808.handler.message;

import com.zzj.nettydemo.jt808.message.Message;
import lombok.Data;
import lombok.AllArgsConstructor;

/**
 * 优先级消息包装类
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
@AllArgsConstructor
public class PriorityMessage implements Comparable<PriorityMessage> {

    /**
     * 原始消息
     */
    private Message message;

    /**
     * 优先级 (数值越小优先级越高)
     */
    private int priority;

    /**
     * 消息类型
     */
    private int messageType;

    /**
     * 创建时间戳
     */
    private long createTimeStamp;

    public PriorityMessage(Message message, int priority, int messageType) {
        this.message = message;
        this.priority = priority;
        this.messageType = messageType;
        this.createTimeStamp = System.currentTimeMillis();
    }

    @Override
    public int compareTo(PriorityMessage other) {
        // 首先按优先级排序（数值越小优先级越高）
        int priorityCompare = Integer.compare(this.priority, other.priority);
        if (priorityCompare != 0) {
            return priorityCompare;
        }
        // 优先级相同时，按时间戳排序（先到先处理）
        return Long.compare(this.createTimeStamp, other.createTimeStamp);
    }

}
