package com.zzj.nettydemo.jt808.handler.config;

import com.zzj.nettydemo.jt808.protocol.Jt808Version;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 消息类型配置
 * <AUTHOR>
 * @date 2025-07-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageTypeConfig {

    /**
     * 消息类型ID
     */
    private int messageType;

    /**
     * 消息版本
     */
    private Jt808Version version;

    /**
     * 优先级 (数值越小优先级越高)
     */
    private int priority;

    /**
     * 最大提交到线程池的数量
     */
    private int maxConcurrentProcessing;

    /**
     * 拒绝策略类型
     */
    private String rejectedExecutionHandler;

    /**
     * 是否启用
     */
    private boolean enabled = true;

    /**
     * 名称（用于标识）
     */
    private String name;

    // 以下字段在统一线程池架构中不再使用，保留用于兼容性
    // 后续可以扩展为一个消息类型的线程池配置类
//    private int corePoolSize = 0;
//    private int maximumPoolSize = 0;
//    private int queueCapacity = 0;
//    private long keepAliveTime = 0L;
}
