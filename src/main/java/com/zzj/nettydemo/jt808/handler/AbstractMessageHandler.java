package com.zzj.nettydemo.jt808.handler;

import com.zzj.nettydemo.jt808.message.Message;
import io.netty.channel.ChannelHandlerContext;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;

/**
 * 消息处理器模板类
 * 提供统一的处理流程和异常处理，支持同步和异步响应
 * 继承此类并实现具体的处理逻辑即可
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
public abstract class AbstractMessageHandler{


    /**
     * 同步处理消息（保持向后兼容）
     * 采用模板方法模式，提供统一的处理流程
     */
    public final void handle(Message message) {
        try {
            // 前置处理
            preProcess(message);

            // 核心处理逻辑
            processMessage(message);

            // 后置处理
            postProcess(message);

        } catch (Exception e) {
            log.error("处理消息 {} 时发生错误", message.getMessageType(), e);
            handleException(message, e);
            throw e; // 重新抛出异常，让上层处理
        }
    }

    /**
     * 异步处理消息（推荐使用）
     * @param message 待处理的消息
     * @param ctx Netty通道上下文，用于回写响应
     * @return CompletableFuture 异步处理结果
     */
    public final CompletableFuture<Void> handleAsync(Message message, ChannelHandlerContext ctx) {
        return CompletableFuture.runAsync(() -> {
            try {
                // 执行同步处理逻辑
                handle(message);

            } catch (Exception e) {
                log.error("异步处理消息 {} 失败", message.getMessageType(), e);
                // 处理错误响应
                throw new RuntimeException("异步消息处理失败", e);
            }
        });
    }


    /**
     * 核心处理逻辑 - 子类必须实现
     */
    protected abstract void processMessage(Message message);


    /**
     * 前置处理 - 子类可选择重写
     */
    protected void preProcess(Message message) {
        // 默认为空，子类可以重写进行参数验证、权限检查等
        log.debug("开始处理消息类型: {}", String.format("0x%04X", message.getMessageType()));
    }

    /**
     * 后置处理 - 子类可选择重写
     */
    protected void postProcess(Message message) {
        // 默认为空，子类可以重写进行日志记录、统计更新等
        log.debug("消息类型 {} 处理完成", String.format("0x%04X", message.getMessageType()));
    }

    /**
     * 异常处理 - 子类可选择重写
     */
    protected void handleException(Message message, Exception e) {
        // 默认异常处理：记录日志
        log.error("处理器 {} 处理消息类型 {} 时发生异常",
                getHandlerName(), String.format("0x%04X", message.getMessageType()), e);
    }

    /**
     * 获取处理器名称 - 用于日志和监控
     */
    protected String getHandlerName() {
        return this.getClass().getSimpleName();
    }

}
