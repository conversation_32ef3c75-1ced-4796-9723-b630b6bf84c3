package com.zzj.nettydemo.jt808.handler;

import com.zzj.nettydemo.jt808.protocol.Jt808Version;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2025-07-16
 * 消息处理器注册注解
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface MessageType {
    int messageType();
    Jt808Version version();
}
