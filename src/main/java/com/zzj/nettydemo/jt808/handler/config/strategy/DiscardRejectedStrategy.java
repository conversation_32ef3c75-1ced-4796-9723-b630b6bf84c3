package com.zzj.nettydemo.jt808.handler.config.strategy;

import lombok.extern.slf4j.Slf4j;

/**
 * 静默丢弃拒绝策略
 * 静默丢弃超出限制的任务，不记录详细日志
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
public class DiscardRejectedStrategy implements SubmissionRejectedStrategy {

    @Override
    public boolean handleRejection(int messageType, int currentCount, int maxLimit, RejectionContext context) {
        // 静默丢弃，只记录debug级别日志
        if (log.isDebugEnabled()) {
            log.debug("消息类型 {} 静默丢弃: 当前提交数={}, 最大限制={}, 终端={}",
                    String.format("0x%04X", messageType),
                    currentCount,
                    maxLimit,
                    context.getOriginalMessage() != null ? context.getOriginalMessage().getHeader().getPhoneNumber() : "未知");
        }

        context.setRejectionReason("达到并发处理限制，静默丢弃");

        return false; // 拒绝处理
    }

    @Override
    public String getStrategyName() {
        return "DISCARD";
    }

    @Override
    public String getDescription() {
        return "静默丢弃策略：静默丢弃超出限制的任务，减少日志噪声";
    }
}
