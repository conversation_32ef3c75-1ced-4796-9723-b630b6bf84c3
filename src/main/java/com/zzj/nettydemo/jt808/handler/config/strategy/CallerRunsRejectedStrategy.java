package com.zzj.nettydemo.jt808.handler.config.strategy;

import lombok.extern.slf4j.Slf4j;

/**
 * 调用者运行拒绝策略
 * 当线程池达到限制时，允许任务在调用者线程（通常是Netty I/O线程）中执行
 * 注意：这种策略可能会影响I/O性能，应谨慎使用
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
public class CallerRunsRejectedStrategy implements SubmissionRejectedStrategy {

    @Override
    public boolean handleRejection(int messageType, int currentCount, int maxLimit, RejectionContext context) {
        log.warn("消息类型 {} 将在调用者线程中运行: 当前提交数={}, 最大限制={}, 终端={}",
                String.format("0x%04X", messageType),
                currentCount,
                maxLimit,
                context.getOriginalMessage() != null ? context.getOriginalMessage().getHeader().getPhoneNumber(): "未知");

        context.setRejectionReason("线程池繁忙，在调用者线程中执行");

        // 检查是否允许降级处理
        if (!context.isAllowFallback()) {
            log.warn("上下文不允许降级处理，拒绝在调用者线程中执行");
            return false;
        }

        // 检查当前线程是否为I/O线程，避免阻塞I/O
        if (isIOThread()) {
            log.warn("当前为I/O线程，为避免阻塞，拒绝在调用者线程中执行消息类型 {}",
                    String.format("0x%04X", messageType));
            context.setRejectionReason("I/O线程不适合执行业务逻辑，拒绝处理");
            return false;
        }

        log.info("允许消息类型 {} 在调用者线程中执行", String.format("0x%04X", messageType));
        return true; // 允许在调用者线程中执行
    }

    /**
     * 检查当前线程是否为I/O线程
     */
    private boolean isIOThread() {
        String threadName = Thread.currentThread().getName();
        // 检查是否为Netty的I/O线程
        return threadName.contains("nioEventLoopGroup") ||
               threadName.contains("epollEventLoopGroup") ||
               threadName.contains("kqueueEventLoopGroup");
    }

    @Override
    public String getStrategyName() {
        return "CALLER_RUNS";
    }

    @Override
    public String getDescription() {
        return "调用者运行策略：在调用者线程中执行任务，可能影响I/O性能";
    }
}
