package com.zzj.nettydemo.jt808.handler.config.strategy;

import lombok.extern.slf4j.Slf4j;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 自适应拒绝策略
 * 根据系统负载动态调整拒绝行为
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
public class AdaptiveRejectedStrategy implements SubmissionRejectedStrategy {

    private final ConcurrentHashMap<Integer, AtomicLong> rejectionCounts = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Integer, AtomicLong> lastRejectionTime = new ConcurrentHashMap<>();

    // 自适应参数
    private final long adaptiveWindowMs = 10000; // 10秒窗口
    private final int lowLoadThreshold = 3; // 低负载阈值
    private final int highLoadThreshold = 10; // 高负载阈值

    @Override
    public boolean handleRejection(int messageType, int currentCount, int maxLimit, RejectionContext context) {
        long currentTime = System.currentTimeMillis();

        // 更新拒绝统计
        AtomicLong rejections = rejectionCounts.computeIfAbsent(messageType, k -> new AtomicLong(0));
        AtomicLong lastTime = lastRejectionTime.computeIfAbsent(messageType, k -> new AtomicLong(currentTime));

        long timeSinceLastRejection = currentTime - lastTime.get();

        // 如果超过窗口期，重置计数
        if (timeSinceLastRejection > adaptiveWindowMs) {
            rejections.set(0);
            lastTime.set(currentTime);
        }

        long currentRejections = rejections.incrementAndGet();

        // 根据拒绝频率决定策略
        SubmissionRejectedStrategy strategy = selectAdaptiveStrategy(messageType, currentRejections, currentCount, maxLimit);

        log.info("消息类型 {} 自适应策略选择: {}, 窗口期内拒绝次数={}, 当前提交数={}, 最大限制={}",
                String.format("0x%04X", messageType),
                strategy.getStrategyName(),
                currentRejections,
                currentCount,
                maxLimit);

        context.setRejectionReason("自适应策略 - " + strategy.getDescription());

        return strategy.handleRejection(messageType, currentCount, maxLimit, context);
    }

    /**
     * 根据系统负载选择合适的策略
     */
    private SubmissionRejectedStrategy selectAdaptiveStrategy(int messageType, long rejectionCount, int currentCount, int maxLimit) {
        double loadRatio = (double) currentCount / maxLimit;

        if (rejectionCount <= lowLoadThreshold && loadRatio < 0.8) {
            // 低负载：使用等待重试策略
            return new WaitRetryRejectedStrategy(50, 2);
        } else if (rejectionCount <= highLoadThreshold && loadRatio < 0.95) {
            // 中等负载：尝试丢弃最老任务
            return new DiscardOldestRejectedStrategy();
        } else {
            // 高负载：直接拒绝
            return new AbortRejectedStrategy();
        }
    }

    @Override
    public String getStrategyName() {
        return "ADAPTIVE";
    }

    @Override
    public String getDescription() {
        return "自适应策略：根据系统负载动态选择最佳拒绝策略";
    }

    /**
     * 获取指定消息类型的拒绝统计
     */
    public long getRejectionCount(int messageType) {
        AtomicLong count = rejectionCounts.get(messageType);
        return count != null ? count.get() : 0;
    }
}
