package com.zzj.nettydemo.jt808.handler.config.strategy;

import com.zzj.nettydemo.jt808.message.Message;
import io.netty.channel.ChannelHandlerContext;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RejectionContext {

    private Message originalMessage;
    private ChannelHandlerContext channelContext;
//    private MessagePriorityThreadPool threadPool;

    @Builder.Default
    private long rejectionTime = System.currentTimeMillis();

    private String rejectionReason;

    @Builder.Default
    private boolean allowFallback = true;

    @Builder.Default
    private int retryCount = 0;

    @Builder.Default
    private int maxRetryCount = 3;
}
