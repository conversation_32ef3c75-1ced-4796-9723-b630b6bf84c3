package com.zzj.nettydemo.jt808.handler.config.strategy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 拒绝策略工厂
 * 负责创建和管理各种拒绝策略实例
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
@Component
public class RejectedStrategyFactory {

    private static final Map<String, SubmissionRejectedStrategy> STRATEGY_CACHE = new ConcurrentHashMap<>();

    static {
        // 预创建所有策略实例
        registerStrategy(new AbortRejectedStrategy());
        registerStrategy(new DiscardRejectedStrategy());
        registerStrategy(new DiscardOldestRejectedStrategy());
        registerStrategy(new CallerRunsRejectedStrategy());
        registerStrategy(new WaitRetryRejectedStrategy());
        registerStrategy(new AdaptiveRejectedStrategy());
    }

    /**
     * 注册拒绝策略
     */
    private static void registerStrategy(SubmissionRejectedStrategy strategy) {
        STRATEGY_CACHE.put(strategy.getStrategyName().toUpperCase(), strategy);
        log.debug("注册拒绝策略: {} - {}", strategy.getStrategyName(), strategy.getDescription());
    }

    /**
     * 根据策略名称创建拒绝策略
     */
    public static SubmissionRejectedStrategy createStrategy(String strategyName) {
        if (strategyName == null || strategyName.trim().isEmpty()) {
            log.warn("策略名称为空，使用默认的ABORT策略");
            return STRATEGY_CACHE.get("ABORT");
        }

        String upperStrategyName = strategyName.toUpperCase().trim();
        SubmissionRejectedStrategy strategy = STRATEGY_CACHE.get(upperStrategyName);

        if (strategy == null) {
            log.warn("未找到拒绝策略: {}, 使用默认的ABORT策略", strategyName);
            return STRATEGY_CACHE.get("ABORT");
        }

        log.debug("创建拒绝策略: {}", strategy.getStrategyName());
        return strategy;
    }

    /**
     * 创建带参数的策略
     */
    public static SubmissionRejectedStrategy createStrategyWithParams(String strategyName, Map<String, Object> params) {
        switch (strategyName.toUpperCase()) {
            case "WAIT_RETRY":
                long waitTime = (Long) params.getOrDefault("waitTime", 100L);
                int maxRetry = (Integer) params.getOrDefault("maxRetry", 3);
                return new WaitRetryRejectedStrategy(waitTime, maxRetry);
            default:
                return createStrategy(strategyName);
        }
    }

    /**
     * 获取所有可用的策略名称
     */
    public static String[] getAvailableStrategies() {
        return STRATEGY_CACHE.keySet().toArray(new String[0]);
    }

    /**
     * 获取策略描述信息
     */
    public static String getStrategyDescription(String strategyName) {
        SubmissionRejectedStrategy strategy = STRATEGY_CACHE.get(strategyName.toUpperCase());
        return strategy != null ? strategy.getDescription() : "未知策略";
    }
}
