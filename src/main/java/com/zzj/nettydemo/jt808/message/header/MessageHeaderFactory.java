package com.zzj.nettydemo.jt808.message.header;

import com.zzj.nettydemo.jt808.protocol.Jt808Version;
import com.zzj.nettydemo.jt808.protocol.VersionDetectorUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * JT808消息头工厂类
 * <AUTHOR>
 */
@Slf4j
public class MessageHeaderFactory {

    /**
     * 从原始数据中解析版本信息并创建对应版本消息头
     */
    public static AbstractMessageHeader createHeaderFromRawData(byte[] data) throws Exception {
        // 使用统一版本检测器
        Jt808Version version = VersionDetectorUtil.detectVersionFromRawData(data);
        return createHeaderByVersion(version);
    }

    /**
     * 根据具体版本直接创建消息头
     */
    public static AbstractMessageHeader createHeaderByVersion(Jt808Version version) {
        return switch (version) {
            case V2013 -> new MessageHeaderV2013();
            case V2019 -> new MessageHeaderV2019();
        };
    }

}
