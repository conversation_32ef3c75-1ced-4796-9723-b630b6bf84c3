package com.zzj.nettydemo.jt808.message.body.v19;

import com.zzj.nettydemo.jt808.message.body.AbstractMessageBody;
import com.zzj.nettydemo.jt808.message.body.MessageBody;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.nio.ByteBuffer;

import static com.zzj.nettydemo.jt808.protocol.Jt808Version.V2019;

/**
 * <AUTHOR>
 * @date 2025-07-21
 * 平台通用应答消息体 (0x8001)
 * 用于平台对终端消息的通用应答
 */
@MessageBody(messageType = 0x8001, version = V2019,
            description = "平台通用应答消息体",
            bodyLength = 5)
@Data
@NoArgsConstructor
public class V2019PlatformCommonResponseMessageBody extends AbstractMessageBody {

    /**
     * 应答流水号（起始字节0）
     * 数据类型：WORD（无符号双字节整型）
     * 描述：用于和对应的终端消息流水号匹配，明确是对哪条终端消息的应答
     */
    private int responseFlowId;

    /**
     * 应答ID（起始字节2）
     * 数据类型：WORD（无符号双字节整型）
     * 描述：对应终端消息的ID，标识具体应答的是哪类终端消息
     */
    private int responseMessageType;

    /**
     * 结果（起始字节4）
     * 数据类型：BYTE（单字节）
     * 描述：应答结果，0=成功/确认，1=失败，2=消息有误，3=不支持，4=报警处理确认
     */
    private byte result;

    /**
     * 应答结果枚举
     */
    public enum ResponseResult {
        SUCCESS(0x00, "成功/确认"),
        FAILURE(0x01, "失败"),
        MESSAGE_ERROR(0x02, "消息有误"),
        NOT_SUPPORTED(0x03, "不支持"),
        ALARM_CONFIRMATION(0x04, "报警处理确认");

        private final byte code;
        private final String description;

        ResponseResult(int code, String description) {
            this.code = (byte) code;
            this.description = description;
        }

        public byte getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        /**
         * 根据代码获取对应的应答结果枚举
         */
        public static ResponseResult fromCode(byte code) {
            for (ResponseResult result : values()) {
                if (result.code == code) {
                    return result;
                }
            }
            throw new IllegalArgumentException("未知的应答结果代码: " + code);
        }
    }

    /**
     * 构造函数
     */
    public V2019PlatformCommonResponseMessageBody(int responseSerialNumber, int responseMessageId, ResponseResult result) {
        this.responseFlowId = responseSerialNumber;
        this.responseMessageType = responseMessageId;
        this.result = result.getCode();
    }

    /**
     * 构造函数
     */
    public V2019PlatformCommonResponseMessageBody(int responseSerialNumber, int responseMessageId, byte result) {
        this.responseFlowId = responseSerialNumber;
        this.responseMessageType = responseMessageId;
        this.result = result;
    }

    @Override
    public byte[] encode() {
        // 总长度：2(应答流水号) + 2(应答ID) + 1(结果) = 5字节
        ByteBuffer buffer = ByteBuffer.allocate(5);

        // 使用大端模式
        buffer.order(java.nio.ByteOrder.BIG_ENDIAN);

        // 1. 应答流水号（WORD，2字节）
        buffer.putShort((short) responseFlowId);

        // 2. 应答ID（WORD，2字节）
        buffer.putShort((short) responseMessageType);

        // 3. 结果（BYTE，1字节）
        buffer.put(result);

        return buffer.array();
    }

    /**
     * 将byte数组反序列化为PlatformCommonResponseMessageBody对象
     * @param data 符合JT/T 808-2019平台通用应答消息体格式的byte数组
     * @throws IllegalArgumentException 当字节数组长度不足或格式错误时抛出
     */
    @Override
    public void decode(byte[] data) throws IllegalArgumentException {
        // 固定长度：5字节
        if (data == null || data.length != 5) {
            throw new IllegalArgumentException("无效的平台通用应答消息体数据，长度必须为5字节");
        }

        ByteBuffer buffer = ByteBuffer.wrap(data);
        // 使用大端模式
        buffer.order(java.nio.ByteOrder.BIG_ENDIAN);

        // 1. 应答流水号（WORD，2字节，起始0）
        setResponseFlowId(buffer.getShort() & 0xFFFF);

        // 2. 应答ID（WORD，2字节，起始2）
        setResponseMessageType(buffer.getShort() & 0xFFFF);

        // 3. 结果（BYTE，1字节，起始4）
        setResult(buffer.get());
    }

    /**
     * 获取应答结果的枚举表示
     */
    public ResponseResult getResponseResult() {
        try {
            return ResponseResult.fromCode(result);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * 设置应答结果（枚举方式）
     */
    public void setResponseResult(ResponseResult responseResult) {
        this.result = responseResult.getCode();
    }

    /**
     * 获取应答结果描述
     */
    public String getResultDescription() {
        ResponseResult responseResult = getResponseResult();
        return responseResult != null ? responseResult.getDescription() : "未知结果(" + result + ")";
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return result == ResponseResult.SUCCESS.getCode();
    }

    /**
     * 判断是否失败
     */
    public boolean isFailure() {
        return result == ResponseResult.FAILURE.getCode();
    }

    /**
     * 判断是否消息有误
     */
    public boolean isMessageError() {
        return result == ResponseResult.MESSAGE_ERROR.getCode();
    }

    /**
     * 判断是否不支持
     */
    public boolean isNotSupported() {
        return result == ResponseResult.NOT_SUPPORTED.getCode();
    }

    /**
     * 判断是否为报警处理确认
     */
    public boolean isAlarmConfirmation() {
        return result == ResponseResult.ALARM_CONFIRMATION.getCode();
    }

    @Override
    public String toString() {
        return "V2019PlatformCommonResponseMessageBody{" +
                "responseSerialNumber=" + responseFlowId +
                ", responseMessageId=0x" + Integer.toHexString(responseMessageType).toUpperCase() +
                ", result=" + result +
                ", resultDescription='" + getResultDescription() + '\'' +
                '}';
    }
}
