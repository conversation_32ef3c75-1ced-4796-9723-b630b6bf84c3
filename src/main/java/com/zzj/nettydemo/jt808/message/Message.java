package com.zzj.nettydemo.jt808.message;

import com.zzj.nettydemo.jt808.message.body.AbstractMessageBody;
import com.zzj.nettydemo.jt808.message.header.AbstractMessageHeader;
import com.zzj.nettydemo.jt808.protocol.Jt808Version;
import lombok.Getter;
import lombok.Setter;

/**
 * 通用JT808消息实现类
 * 通过组合消息头和消息体来构建完整消息
 */
@Getter
@Setter
public class Message  {
    private AbstractMessageHeader header;
    private AbstractMessageBody body;

    //消息类型
    private final int messageType;

    //消息版本
    private final Jt808Version version;

    public Message(int messageType, Jt808Version version) {
        this.messageType = messageType;
        this.version = version;
    }

    public byte[] encode() {
        //todo
        return null;
    }

    public void decode(byte[] data) {
        if (header == null || body == null) {
            throw new IllegalStateException("消息头或消息体未设置");
        }

        // 解码消息头
        header.decode(data);

        // 计算消息体位置
        int headerLength = header.getHeaderLength();
        int bodyLength = header.getProperties().getBodyLength();

        // 解码消息体
        byte[] bodyData = new byte[bodyLength];
        System.arraycopy(data, headerLength, bodyData, 0, bodyLength);
        body.decode(bodyData);

    }

}
