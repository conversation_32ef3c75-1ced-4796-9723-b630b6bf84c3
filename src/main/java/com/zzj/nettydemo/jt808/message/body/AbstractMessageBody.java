package com.zzj.nettydemo.jt808.message.body;

import com.zzj.nettydemo.jt808.protocol.Jt808Version;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * JT808消息体抽象基类
 * 消息体的版本和消息类型由子类通过注解指定
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public abstract class AbstractMessageBody {
    /**
     * 编码消息体
     * 不是所有的消息体类型都需要实现此方法，
     */
    public abstract byte[] encode();

    /**
     * 解码消息体
     * 不是所有的消息体类型都需要实现此方法，
     */
    public abstract void decode(byte[] data);

}
