package com.zzj.nettydemo.jt808.message.response;

import com.zzj.nettydemo.jt808.message.Message;
import com.zzj.nettydemo.jt808.message.body.v19.V2019PlatformCommonResponseMessageBody;
import com.zzj.nettydemo.jt808.message.header.AbstractMessageHeader;
import com.zzj.nettydemo.jt808.message.header.MessageHeaderFactory;
import com.zzj.nettydemo.jt808.message.header.MessageProperties;
import lombok.extern.slf4j.Slf4j;


/**
 * 平台通用应答消息构建器 (0x8001)
 * 负责构建符合JT808协议的平台通用应答消息
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Slf4j
public class PlatformCommonResponseBuilder {

    /**
     * 构建平台通用应答消息
     *
     * @param originalMessage 原始终端消息
     * @param result 应答结果
     * @return 编码后的平台通用应答消息字节数组
     */
    public static byte[] buildResponse(Message originalMessage, V2019PlatformCommonResponseMessageBody.ResponseResult result) {
        try {
            // 1. 构建消息体
            byte[] messageBody = buildResponseBody(
                originalMessage.getHeader().getFlowId(),
                originalMessage.getMessageType(),
                result
            );

            // 2. 构建消息头
            byte[] messageHeader = buildResponseHeader(originalMessage, messageBody.length);

            // 3. 拼接完整消息
            byte[] fullMessage = new byte[messageHeader.length + messageBody.length];
            System.arraycopy(messageHeader, 0, fullMessage, 0, messageHeader.length);
            System.arraycopy(messageBody, 0, fullMessage, messageHeader.length, messageBody.length);

            log.info("构建平台通用应答消息完成，结果: {}, 消息总长度: {}",
                    result.getDescription(), fullMessage.length);

            return fullMessage;

        } catch (Exception e) {
            log.error("构建平台通用应答消息失败", e);
            throw new RuntimeException("构建平台通用应答消息失败", e);
        }
    }

    /**
     * 构建平台通用应答消息体
     */
    private static byte[] buildResponseBody(int responseSerialNumber, int responseMessageId,
                                          V2019PlatformCommonResponseMessageBody.ResponseResult result) {
        V2019PlatformCommonResponseMessageBody responseBody = new V2019PlatformCommonResponseMessageBody(
            responseSerialNumber,
            responseMessageId,
            result
        );

        return responseBody.encode();
    }

    /**
     * 构建平台通用应答消息头
     */
    private static byte[] buildResponseHeader(Message originalMessage, int bodyLength) {
        try {
            // 创建消息属性
            MessageProperties properties = new MessageProperties();
            properties.setBodyLength(bodyLength);
            properties.setUseEncrypt(0);
            properties.setHasSubPackage(false);
            properties.setHasVersionFlag(true);

            // 使用原消息的版本和手机号构建响应头
            AbstractMessageHeader responseHeader = MessageHeaderFactory.createHeaderByVersion(originalMessage.getVersion());
            responseHeader.setMessageType(0x8001);
            responseHeader.setProperties(properties);
            responseHeader.setVersion(originalMessage.getVersion());
            responseHeader.setPhoneNumber(originalMessage.getHeader().getPhoneNumber());
            responseHeader.setFlowId(generateResponseFlowId());

            return responseHeader.encode();

        } catch (Exception e) {
            log.error("构建平台通用应答消息头失败", e);
            throw new RuntimeException("构建平台通用应答消息头失败", e);
        }
    }

    /**
     * 构建成功应答
     */
    public static byte[] buildSuccessResponse(Message originalMessage) {
        return buildResponse(originalMessage, V2019PlatformCommonResponseMessageBody.ResponseResult.SUCCESS);
    }

    /**
     * 构建失败应答
     */
    public static byte[] buildFailureResponse(Message originalMessage) {
        return buildResponse(originalMessage, V2019PlatformCommonResponseMessageBody.ResponseResult.FAILURE);
    }

    /**
     * 构建消息有误应答
     */
    public static byte[] buildMessageErrorResponse(Message originalMessage) {
        return buildResponse(originalMessage, V2019PlatformCommonResponseMessageBody.ResponseResult.MESSAGE_ERROR);
    }

    /**
     * 构建不支持应答
     */
    public static byte[] buildNotSupportedResponse(Message originalMessage) {
        return buildResponse(originalMessage, V2019PlatformCommonResponseMessageBody.ResponseResult.NOT_SUPPORTED);
    }

    /**
     * 生成应答消息的流水号
     */
    private static int generateResponseFlowId() {
        return (int) (System.currentTimeMillis() % 65536);
    }
}
