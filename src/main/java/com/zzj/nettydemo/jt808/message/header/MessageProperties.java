package com.zzj.nettydemo.jt808.message.header;

import lombok.Data;

/**
 * 消息体属性
 * 假设不同版本的消息头的消息体属性格式都是一样的。
 * 固定2字节
 */
@Data
public class MessageProperties {
    /** 消息体长度 */
    private int bodyLength;

    /** 数据加密方式
     *  0:不加密
     *  1:RSA加密
     */
    private int useEncrypt;

    /** 是否分包 */
    private boolean hasSubPackage;

    /** 版本标识 */
    private boolean hasVersionFlag;

    /** 保留位 */
    private int reserved;

    /**
     * 从属性值解析
     */
    public static MessageProperties decode(int attributes) {
        MessageProperties props = new MessageProperties();

        // 消息体长度是低10位
        props.bodyLength = attributes & 0x03FF;

        // 加密方式是第10-12位
        // 都为0,表示不加密
        // 第10位为1，表示消息体经过RSA加密
        // 其他为保留位
        // 右移10位，去除低10位，然后和0x07进行与操作，得到第10-12位的值
        props.useEncrypt = (attributes >> 10) & 0x07;

        // 第13位表示是否分包
        props.hasSubPackage = ((attributes >> 13) & 0x01) == 1;

        // 第14位表示是否有版本标识
        props.hasVersionFlag = ((attributes >> 14) & 0x01) == 1;
        // 第15位为保留位
        props.reserved = (attributes >> 15) & 0x01;
        return props;
    }

    /**
     * 转换为属性值（实例方法）
     */
    public int encode() {
        return encode(this);
    }

    /**
     * 转换为属性值（静态方法）
     */
    public static int encode(MessageProperties properties) {
        int attributes = 0;
        attributes |= (properties.bodyLength & 0x03FF);
        attributes |= ((properties.useEncrypt & 0x07) << 10);
        attributes |= (properties.hasSubPackage ? 1 : 0) << 13;
        attributes |= (properties.hasVersionFlag ? 1 : 0) << 14;
        attributes |= ((properties.reserved & 0x01) << 15);
        return attributes;
    }

}
