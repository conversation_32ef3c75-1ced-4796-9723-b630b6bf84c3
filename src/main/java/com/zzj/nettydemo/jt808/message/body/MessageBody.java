package com.zzj.nettydemo.jt808.message.body;

import com.zzj.nettydemo.jt808.protocol.Jt808Version;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 消息体注解，用于自动注册消息体
 * <AUTHOR>
 * @date 2025-07-16
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface MessageBody {
    /** 消息ID */
    int messageType();

    /** 协议版本 */
    Jt808Version version();

    /** 消息描述 */
    String description();

    /** 消息体长度 -1表示消息体长度不定*/
    int bodyLength() ;
}
