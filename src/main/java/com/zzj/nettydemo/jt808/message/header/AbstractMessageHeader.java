package com.zzj.nettydemo.jt808.message.header;

import com.zzj.nettydemo.jt808.protocol.Jt808Version;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-07-16
 * 消息体抽象类
 * 如果消息头的格式有变化，那么就创建一个对应的子类即可
 * 如果子类的格式有破坏性变更，那么就使用子类的
 * 消息头的具体格式看子类的decode实现，
 */
@Data
public abstract class AbstractMessageHeader {
    //协议中的消息ID,表示的是消息类型
    protected int messageType;

    // 消息体属性 ,假设不同版本的消息头的消息体属性格式都是一样的。
    protected MessageProperties properties;

    //协议版本
    protected Jt808Version version;

    //终端手机号
    protected String phoneNumber;

    //消息流水号,从0开始
    protected int flowId;

    // 分包信息（可选）
    protected PacketFragment packetFragment;

    @Data
    @AllArgsConstructor
    public static class PacketFragment {
        /** 总包数 */
        private int totalPackages;

        /** 包序号
         *  分包索引从1开始
         */
        private int packageSequence;
    }


    /**
     * 编码消息头并且返回
     */
    public abstract byte[] encode();

    /**
     * 解码消息头,并保存到当前对象中
     */
    public abstract void decode(byte[] data);

    /**
     * 获取加密方式
     */
    public int getEncryptType(){
        if(properties == null){
            throw  new NullPointerException("消息头的消息体属性未初始化");
        }
        return properties.getUseEncrypt();
    }

    /**
     * 获取消息头长度
     */
    public abstract int getHeaderLength();

    /**
     * 获取消息体长度
     */
    public int getBodyLength(){
        if(properties == null){
            throw  new NullPointerException("消息头的消息体属性未初始化");
        }
        return properties.getBodyLength();
    }
}
