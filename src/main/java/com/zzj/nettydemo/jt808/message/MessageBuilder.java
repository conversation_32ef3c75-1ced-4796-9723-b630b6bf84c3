package com.zzj.nettydemo.jt808.message;

import com.zzj.nettydemo.jt808.message.body.AbstractMessageBody;
import com.zzj.nettydemo.jt808.message.body.MessageBodyFactory;
import com.zzj.nettydemo.jt808.message.header.AbstractMessageHeader;
import com.zzj.nettydemo.jt808.message.header.MessageHeaderFactory;
import com.zzj.nettydemo.jt808.protocol.Jt808Version;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * JT808消息建造者
 * 使用建造者模式简化消息创建和解析流程
 * 主要功能：
 * 1. 从原始数据自动解析并构建完整消息
 * 2. 自动检测协议版本
 * 3. 自动从工厂创建对应的消息头和消息体
 * 4. 提供便捷的静态方法用于快速创建和解析
 *
 * <AUTHOR>
 */
@Slf4j
public class MessageBuilder {
    private byte[] rawData;
    private AbstractMessageHeader header;
    private AbstractMessageBody body;

    private MessageBuilder() {}

    /**
     * 创建消息建造者
     */
    public static MessageBuilder builder() {
        return new MessageBuilder();
    }

    /**
     * 从原始数据构建消息
     * 自动检测版本并准备解析
     * @param data 原始数据包
     * @return 建造者实例
     */
    public static MessageBuilder data(byte[] data) {
        MessageBuilder builder = new MessageBuilder();
        builder.rawData = data;
        return builder;
    }

    /**
     * 快速解析方法 - 从原始数据直接构建消息
     * 这是最常用的方法，只需要传入原始数据，自动完成所有解析工作
     * @param data 原始数据包
     * @return 解析后的完整消息
     */
    @SneakyThrows
    public static Message parse(byte[] data) {
        if (data == null || data.length == 0) {
            throw new IllegalArgumentException("原始数据不能为空");
        }

        log.debug("开始解析原始数据，数据长度: {} 字节", data.length);
        return data(data).build();
    }

    /**
     * 构建消息
     * 自动从消息体工厂和消息头工厂组合出完整的消息类
     * @return 完整的消息对象
     */
    public Message build() throws Exception{
            // 校验基础数据
            if (rawData == null) {
                throw new IllegalArgumentException("原始数据不能为空");
            }

            // 创建消息头
            if (header == null) {
                // 从原始数据解析消息头
                header = MessageHeaderFactory.createHeaderFromRawData(rawData);
                header.decode(rawData);
            }

            // 从消息头获取关键信息
            int messageType = header.getMessageType();
            Jt808Version version = header.getVersion();

            // 创建消息体
            if (body == null) {
                body = MessageBodyFactory.createBody(messageType, version);

                // 计算消息体位置并解码
                int headerLength = header.getHeaderLength();
                int bodyLength = header.getProperties().getBodyLength();

                if (bodyLength > 0 && rawData.length >= headerLength + bodyLength) {
                    byte[] bodyData = new byte[bodyLength];
                    System.arraycopy(rawData, headerLength, bodyData, 0, bodyLength);
                    body.decode(bodyData);
                } else if (bodyLength == 0) {
                    log.debug("消息体长度为0，跳过解码");
                } else {
                    log.warn("数据长度不足，无法解码消息体（需要: {}，实际: {}）",
                            headerLength + bodyLength, rawData.length);
                }
            }

            // 创建并返回消息对象
            Message message = new Message(messageType, version);
            message.setHeader(header);
            message.setBody(body);

            return message;

    }
}
