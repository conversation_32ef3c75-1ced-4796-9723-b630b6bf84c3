package com.zzj.nettydemo.jt808.message.body;

import cn.hutool.core.util.ClassUtil;
import com.zzj.nettydemo.jt808.protocol.Jt808Version;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * JT808消息体工厂类
 * <AUTHOR>
 */
@Slf4j
public class MessageBodyFactory {

    /** 消息体类注册表：(版本+消息类型) → 消息体类
     * 只会发生多线程读，因此普通的HashMap即可
     */
    private static final Map<String, Class<? extends AbstractMessageBody>> BODY_CLASS_REGISTRY =
            new HashMap<>();

/*
     这里使用了缓存机制，创建实例开销更低。
     但是现代JVM对反射优化显著，而且消息体类较为简单，因此缓存效果不明显。

        private static final Map<Jt808Version, Map<Integer, MessageBodyInfo>> BODY_REGISTRY =
                new ConcurrentHashMap<>();

    private static final Map<String, AbstractMessageBody> BODY_CACHE =
            new ConcurrentHashMap<>();
       */

    static {
        //初始化注册所有消息体
        // 自动扫描并注册带有@MessageBody注解的类
        scanAndRegisterBodies();
        log.info("消息体工厂初始化完成，共注册 {} 个消息体", BODY_CLASS_REGISTRY.size());
    }

    /**
     * 扫描并自动注册消息体
     */
    private static void scanAndRegisterBodies() {
        Set<Class<?>> classes = ClassUtil.scanPackage("com.zzj.nettydemo.jt808.message.body");

        for (Class<?> clazz : classes) {
            MessageBody annotation = clazz.getAnnotation(MessageBody.class);
            // 检查是否为消息体类
            if (annotation != null && AbstractMessageBody.class.isAssignableFrom(clazz)) {
                // 转换为具体消息体类
                @SuppressWarnings("unchecked")
                Class<? extends AbstractMessageBody> bodyClass =
                        (Class<? extends AbstractMessageBody>) clazz;

                String key = generateKey(annotation.version(), annotation.messageType());
                BODY_CLASS_REGISTRY.put(key, bodyClass);
            }
        }
    }

    /**
     * 根据消息ID和版本创建消息体
     */
    public static AbstractMessageBody createBody(int messageType, Jt808Version version) {
        String key = generateKey(version, messageType);
        Class<? extends AbstractMessageBody> bodyClass = BODY_CLASS_REGISTRY.get(key);

        if (bodyClass == null) {
            throw new IllegalArgumentException(
                    String.format("不支持的消息类型: 0x%04X (版本: %s)", messageType, version));
        }

        try {
            return bodyClass.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException("创建消息体实例失败", e);
        }
    }

    private static String generateKey(Jt808Version version, int messageType) {
        return String.format("%s_0x%04X", version.name(), messageType);
    }

}
