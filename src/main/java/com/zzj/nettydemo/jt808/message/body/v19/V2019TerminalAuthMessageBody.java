package com.zzj.nettydemo.jt808.message.body.v19;

import com.zzj.nettydemo.jt808.message.body.AbstractMessageBody;
import com.zzj.nettydemo.jt808.message.body.MessageBody;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

import static com.zzj.nettydemo.jt808.protocol.Jt808Version.V2019;

/**
 * <AUTHOR>
 * @date 2025-07-21
 * 终端鉴权消息体
 */
@MessageBody(messageType = 0x0102, version = V2019,
            description = "终端鉴权消息体",
            bodyLength = -1)
@Data
@NoArgsConstructor
public class V2019TerminalAuthMessageBody extends AbstractMessageBody {

    /**
     * 鉴权码长度（起始字节0）
     * 数据类型：BYTE（单字节）
     * 描述：用于标识后续鉴权码内容的字节长度
     */
    private byte authCodeLength;

    /**
     * 鉴权码内容（起始字节1）
     * 数据类型：STRING
     * 描述：长度由鉴权码长度字段的值决定，存储实际用于鉴权的编码内容
     */
    private String authCode;

    /**
     * 终端IMEI（起始字节 n+1）
     * 数据类型：BYTE[15]（15字节）
     * 描述：用于唯一标识终端设备的国际移动设备识别码
     */
    private byte[] terminalImei;

    /**
     * 软件版本号（起始字节 n+16）
     * 数据类型：BYTE[20]（20字节）
     * 描述：存储厂家自定义的终端软件版本信息，若版本号位数不足20字节，后续补0x00填充
     */
    private byte[] softwareVersion;

    @Override
    public byte[] encode() {
        if (authCode == null || terminalImei == null || softwareVersion == null) {
            throw new IllegalStateException("鉴权消息体字段不能为空");
        }

        // 获取鉴权码的字节数组
        byte[] authCodeBytes = authCode.getBytes(Charset.forName("GBK"));
        byte authCodeLen = (byte) authCodeBytes.length;

        // 计算总长度：1(鉴权码长度) + n(鉴权码内容) + 15(IMEI) + 20(软件版本)
        int totalLength = 1 + authCodeLen + 15 + 20;
        ByteBuffer buffer = ByteBuffer.allocate(totalLength);

        // 1. 鉴权码长度（1字节）
        buffer.put(authCodeLen);

        // 2. 鉴权码内容（n字节）
        buffer.put(authCodeBytes);

        // 3. 终端IMEI（15字节）
        if (terminalImei.length != 15) {
            throw new IllegalArgumentException("终端IMEI必须为15字节");
        }
        buffer.put(terminalImei);

        // 4. 软件版本号（20字节）
        if (softwareVersion.length != 20) {
            throw new IllegalArgumentException("软件版本号必须为20字节");
        }
        buffer.put(softwareVersion);

        return buffer.array();
    }

    /**
     * 将byte数组反序列化为TerminalAuthMessageBody对象
     * @param data 符合JT/T 808-2019终端鉴权消息体格式的byte数组
     * @throws IllegalArgumentException 当字节数组长度不足或格式错误时抛出
     */
    @Override
    public void decode(byte[] data) throws IllegalArgumentException {
        // 最小长度：1(鉴权码长度) + 0(最小鉴权码) + 15(IMEI) + 20(软件版本) = 36字节
        if (data == null || data.length < 36) {
            throw new IllegalArgumentException("无效的终端鉴权消息体数据，长度不足");
        }

        ByteBuffer buffer = ByteBuffer.wrap(data);

        // 1. 鉴权码长度（1字节）
        byte authCodeLength = buffer.get();
        setAuthCodeLength(authCodeLength);

        // 验证剩余数据长度是否足够
        int expectedLength = 1 + authCodeLength + 15 + 20;
        if (data.length < expectedLength) {
            throw new IllegalArgumentException(
                String.format("数据长度不匹配，期望%d字节，实际%d字节", expectedLength, data.length));
        }

        // 2. 鉴权码内容（n字节）
        if (authCodeLength > 0) {
            byte[] authCodeBytes = new byte[authCodeLength];
            buffer.get(authCodeBytes);
            setAuthCode(new String(authCodeBytes, Charset.forName("GBK")));
        } else {
            setAuthCode("");
        }

        // 3. 终端IMEI（15字节）
        byte[] imeiBytes = new byte[15];
        buffer.get(imeiBytes);
        setTerminalImei(imeiBytes);

        // 4. 软件版本号（20字节）
        byte[] versionBytes = new byte[20];
        buffer.get(versionBytes);
        setSoftwareVersion(versionBytes);
    }

    /**
     * 获取IMEI字符串表示（去除末尾的0x00填充）
     */
    public String getImeiAsString() {
        if (terminalImei == null) {
            return null;
        }
        return new String(terminalImei, StandardCharsets.US_ASCII).replaceAll("\0", "");
    }

    /**
     * 获取软件版本字符串表示（去除末尾的0x00填充）
     */
    public String getSoftwareVersionAsString() {
        if (softwareVersion == null) {
            return null;
        }
        return new String(softwareVersion, StandardCharsets.US_ASCII).replaceAll("\0", "");
    }

    /**
     * 设置IMEI（字符串形式，自动转换为15字节数组）
     */
    public void setImeiFromString(String imei) {
        if (imei == null) {
            this.terminalImei = null;
            return;
        }

        byte[] imeiBytes = new byte[15];
        byte[] sourceBytes = imei.getBytes(StandardCharsets.US_ASCII);
        int copyLength = Math.min(sourceBytes.length, 15);
        System.arraycopy(sourceBytes, 0, imeiBytes, 0, copyLength);
        // 剩余位置自动填充0x00
        this.terminalImei = imeiBytes;
    }

    /**
     * 设置软件版本（字符串形式，自动转换为20字节数组）
     */
    public void setSoftwareVersionFromString(String version) {
        if (version == null) {
            this.softwareVersion = null;
            return;
        }

        byte[] versionBytes = new byte[20];
        byte[] sourceBytes = version.getBytes(StandardCharsets.US_ASCII);
        int copyLength = Math.min(sourceBytes.length, 20);
        System.arraycopy(sourceBytes, 0, versionBytes, 0, copyLength);
        // 剩余位置自动填充0x00
        this.softwareVersion = versionBytes;
    }

    @Override
    public String toString() {
        return "V2019TerminalAuthMessageBody{" +
                "authCodeLength=" + authCodeLength +
                ", authCode='" + authCode + '\'' +
                ", terminalImei=" + getImeiAsString() +
                ", softwareVersion=" + getSoftwareVersionAsString() +
                '}';
    }
}
