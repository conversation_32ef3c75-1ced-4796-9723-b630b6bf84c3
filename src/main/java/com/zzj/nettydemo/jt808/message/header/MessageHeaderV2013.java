package com.zzj.nettydemo.jt808.message.header;

import com.zzj.nettydemo.jt808.protocol.Jt808Version;
import com.zzj.nettydemo.jt808.util.BitUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

/**
 * 2013版本消息头
 */
public class MessageHeaderV2013 extends AbstractMessageHeader {

    {
        this.version = Jt808Version.V2013;
    }


    @Override
    public byte[] encode() {
        //todo
        return null;
    }


    @Override
    public void decode(byte[] data) {
        ByteBuf buffer = Unpooled.copiedBuffer(data);

        // 第0-1字节 2字节
        // 解析消息ID, 消息类型
        messageType = buffer.readUnsignedShort();

        // 第2-3字节 2字节
        // 解析消息体属性
        int attributes = buffer.readUnsignedShort();
        this.properties = MessageProperties.decode(attributes);

        // 第4-9字节 6字节
        // 解析终端手机号 (2013版为6字节)
        byte[] phoneBytes = new byte[6];
        buffer.readBytes(phoneBytes);
        phoneNumber = BitUtil.decode(phoneBytes);

        // 第10-11字节 2字节
        // 解析消息流水号
        flowId = buffer.readUnsignedShort();

        // 第12-15字节 4字节(可选)
        // 解析分包信息
        if (properties.isHasSubPackage()) {
            int totalPackages = buffer.readUnsignedShort();
            int packageSequence = buffer.readUnsignedShort();
            packetFragment =new PacketFragment(totalPackages, packageSequence);
        }
    }

    @Override
    public int getHeaderLength() {
        int length = 12; // 基础长度：消息ID(2) + 属性(2) + 手机号(6) + 流水号(2)
        if (properties != null && properties.isHasSubPackage()) {
            // 分包信息：总包数(2) + 包序号(2)
            length += 4;
        }
        return length;
    }
}
