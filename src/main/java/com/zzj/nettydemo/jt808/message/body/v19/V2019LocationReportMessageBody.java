package com.zzj.nettydemo.jt808.message.body.v19;

import com.zzj.nettydemo.jt808.message.body.AbstractMessageBody;
import com.zzj.nettydemo.jt808.message.body.MessageBody;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.nio.ByteBuffer;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static com.zzj.nettydemo.jt808.protocol.Jt808Version.V2019;

/**
 * <AUTHOR>
 * @date 2025-07-21
 * 位置信息汇报消息体 (0x0200)
 */
@MessageBody(messageType = 0x0200, version = V2019,
            description = "位置信息汇报消息体",
            bodyLength = -1)
@Data
@NoArgsConstructor
public class V2019LocationReportMessageBody extends AbstractMessageBody {

    /**
     * 位置基本信息（固定结构，必选）
     */
    private LocationBasicInfo basicInfo;

    /**
     * 位置附加信息项列表（动态结构，可选）
     */
    private List<LocationAdditionalInfo> additionalInfoList;

    public V2019LocationReportMessageBody(LocationBasicInfo basicInfo) {
        this.basicInfo = basicInfo;
        this.additionalInfoList = new ArrayList<>();
    }

    /**
     * 位置基本信息（固定结构 ）
     */
    @Data
    @NoArgsConstructor
    public static class LocationBasicInfo {

        /**
         * 报警标志（4字节，DWORD类型）
         * 32位整数，每一位代表一种报警类型，参照协议表25
         */
        private int alarmFlag;

        /**
         * 状态（4字节，DWORD类型）
         * 32位整数，每一位代表终端/车辆的运行状态，参照协议表24
         */
        private int status;

        /**
         * 纬度（4字节，DWORD类型）
         * 实际纬度值乘以10^6后的结果，精确到百万分之一度
         */
        private int latitude;

        /**
         * 经度（4字节，DWORD类型）
         * 实际经度值乘以10^6后的结果，精确到百万分之一度
         */
        private int longitude;

        /**
         * 高程（2字节，WORD类型）
         * 车辆所在位置的海拔高度，单位为米
         */
        private short altitude;

        /**
         * 速度（2字节，WORD类型）
         * 车辆行驶速度乘以10后的结果，单位为1/10km/h
         */
        private short speed;

        /**
         * 方向（2字节，WORD类型）
         * 车辆行驶方向角度，范围0~359，正北为0，顺时针递增
         */
        private short direction;

        /**
         * 时间（6字节，BCD编码）
         * BCD编码存储的时间，格式YY-MM-DD-hh-mm-ss，GMT+8时区
         */
        private byte[] time;

        /**
         * 设置实际纬度值（度）
         */
        public void setLatitudeValue(double latitudeValue) {
            this.latitude = Math.toIntExact(Math.round(latitudeValue * 1_000_000));
        }

        /**
         * 获取实际纬度值（度）
         */
        public double getLatitudeValue() {
            return latitude / 1_000_000.0;
        }

        /**
         * 设置实际经度值（度）
         */
        public void setLongitudeValue(double longitudeValue) {
            this.longitude = Math.toIntExact(Math.round(longitudeValue * 1_000_000));
        }

        /**
         * 获取实际经度值（度）
         */
        public double getLongitudeValue() {
            return longitude / 1_000_000.0;
        }

        /**
         * 设置实际速度值（km/h）
         */
        public void setSpeedValue(double speedValue) {
            this.speed = (short) Math.round(speedValue * 10);
        }

        /**
         * 获取实际速度值（km/h）
         */
        public double getSpeedValue() {
            return speed / 10.0;
        }

        /**
         * 设置时间（从LocalDateTime）
         */
        public void setTimeFromDateTime(LocalDateTime dateTime) {
            this.time = encodeBcdTime(dateTime);
        }

        /**
         * 获取时间（转换为LocalDateTime）
         */
        public LocalDateTime getTimeAsDateTime() {
            return decodeBcdTime(time);
        }

        /**
         * 编码BCD时间
         */
        private byte[] encodeBcdTime(LocalDateTime dateTime) {
            byte[] bcdTime = new byte[6];
            bcdTime[0] = (byte) ((dateTime.getYear() % 100)); // YY
            bcdTime[1] = (byte) (dateTime.getMonthValue()); // MM
            bcdTime[2] = (byte) (dateTime.getDayOfMonth()); // DD
            bcdTime[3] = (byte) (dateTime.getHour()); // hh
            bcdTime[4] = (byte) (dateTime.getMinute()); // mm
            bcdTime[5] = (byte) (dateTime.getSecond()); // ss

            // 转换为BCD格式
            for (int i = 0; i < bcdTime.length; i++) {
                int value = bcdTime[i] & 0xFF;
                bcdTime[i] = (byte) (((value / 10) << 4) | (value % 10));
            }

            return bcdTime;
        }

        /**
         * 解码BCD时间
         */
        private LocalDateTime decodeBcdTime(byte[] bcdTime) {
            if (bcdTime == null || bcdTime.length != 6) {
                return null;
            }

            int[] values = new int[6];
            for (int i = 0; i < bcdTime.length; i++) {
                int bcd = bcdTime[i] & 0xFF;
                values[i] = ((bcd >> 4) * 10) + (bcd & 0x0F);
            }

            int year = 2000 + values[0]; // YY转换为完整年份
            return LocalDateTime.of(year, values[1], values[2], values[3], values[4], values[5]);
        }

        /**
         * 编码位置基本信息
         */
        public byte[] encode() {
            ByteBuffer buffer = ByteBuffer.allocate(28);
            buffer.order(java.nio.ByteOrder.BIG_ENDIAN);

            // 报警标志（4字节）
            buffer.putInt((int) alarmFlag);

            // 状态（4字节）
            buffer.putInt((int) status);

            // 纬度（4字节）
            buffer.putInt((int) latitude);

            // 经度（4字节）
            buffer.putInt((int) longitude);

            // 高程（2字节）
            buffer.putShort((short) altitude);

            // 速度（2字节）
            buffer.putShort((short) speed);

            // 方向（2字节）
            buffer.putShort((short) direction);

            // 时间（6字节）
            if (time != null && time.length == 6) {
                buffer.put(time);
            } else {
                // 如果时间未设置或长度不正确，填充0
                buffer.put(new byte[6]);
            }

            return buffer.array();
        }

        /**
         * 解码位置基本信息
         */
        public void decode(byte[] data) {
//            if (data == null || data.length != 28) {
//                throw new IllegalArgumentException("位置基本信息数据长度不足28字节");
//            }

            ByteBuffer buffer = ByteBuffer.wrap(data, 0, 28);
            buffer.order(java.nio.ByteOrder.BIG_ENDIAN);

            // 报警标志（4字节）
            alarmFlag = buffer.getInt();

            // 状态（4字节）
            status = buffer.getInt();

            // 纬度（4字节）
            latitude = buffer.getInt();

            // 经度（4字节）
            longitude = buffer.getInt();

            // 高程（2字节）
            altitude = buffer.getShort();

            // 速度（2字节）
            speed = buffer.getShort();

            // 方向（2字节）
            direction = buffer.getShort();

            // 时间（6字节）
            time = new byte[6];
            buffer.get(time);
        }
    }

    /**
     * 位置附加信息项
     */
    @Data
    @NoArgsConstructor
    public static class LocationAdditionalInfo {

        /**
         * 附加信息ID（1字节，BYTE类型）
         * 标识附加信息的类型，取值范围1~255，参照协议表27
         */
        private byte infoId;

        /**
         * 附加信息长度（1字节，BYTE类型）
         * 表示附加信息内容的字节长度
         */
        private byte infoLength;

        /**
         * 附加信息内容（动态长度）
         * 存储附加信息的实际数据，格式由附加信息ID决定
         */
        private byte[] infoContent;

        /**
         * 构造函数
         */
        public LocationAdditionalInfo(byte infoId, byte[] infoContent) {
            this.infoId = infoId;
            this.infoContent = infoContent;
            this.infoLength = (byte) (infoContent != null ? infoContent.length : 0);
        }

        /**
         * 附加信息类型枚举
         */
        public enum AdditionalInfoType {
            MILEAGE(0x01, "里程", 4),
            FUEL_LEVEL(0x02, "油量", 2),
            SPEED_RECORD(0x03, "行驶记录功能获取的速度", 2),
            ALARM_EVENT_ID(0x04, "需要人工确认报警事件的ID", 2),
            TIRE_PRESSURE(0x05, "胎压", 30),
            TEMPERATURE(0x06, "车厢温度", 2);
            // 其他附加信息类型可以根据需要添加

            private final int id;
            private final String description;
            private final int length;

            AdditionalInfoType(int id, String description, int length) {
                this.id = id;
                this.description = description;
                this.length = length;
            }

            public byte getId() {
                return (byte) id;
            }

            public String getDescription() {
                return description;
            }

            public int getLength() {
                return length;
            }

            public static AdditionalInfoType fromId(byte id) {
                for (AdditionalInfoType type : values()) {
                    if (type.getId() == id) {
                        return type;
                    }
                }
                return null;
            }
        }

        /**
         * 编码附加信息项
         */
        public byte[] encode() {
            int totalLength = 2 + (infoContent != null ? infoContent.length : 0);
            ByteBuffer buffer = ByteBuffer.allocate(totalLength);

            // 附加信息ID（1字节）
            buffer.put(infoId);

            // 附加信息长度（1字节）
            buffer.put(infoLength);

            // 附加信息内容
            if (infoContent != null) {
                buffer.put(infoContent);
            }

            return buffer.array();
        }

        /**
         * 解码附加信息项
         */
        public static LocationAdditionalInfo decode(ByteBuffer buffer) {
            if (buffer.remaining() < 2) {
                return null;
            }

            LocationAdditionalInfo info = new LocationAdditionalInfo();

            // 附加信息ID（1字节）
            info.infoId = buffer.get();

            // 附加信息长度（1字节）
            info.infoLength = buffer.get();

            // 附加信息内容
            int contentLength = info.infoLength ;
            if (buffer.remaining() >= contentLength) {
                info.infoContent = new byte[contentLength];
                buffer.get(info.infoContent);
            }

            return info;
        }
    }

    @Override
    public byte[] encode() {
        if (basicInfo == null) {
            throw new IllegalStateException("位置基本信息不能为空");
        }

        // 计算总长度
        int totalLength = 28; // 基本信息固定28字节
        if (additionalInfoList != null) {
            for (LocationAdditionalInfo info : additionalInfoList) {
                totalLength += 2 + (info.getInfoContent() != null ? info.getInfoContent().length : 0);
            }
        }

        ByteBuffer buffer = ByteBuffer.allocate(totalLength);

        // 1. 编码位置基本信息
        buffer.put(basicInfo.encode());

        // 2. 编码位置附加信息项列表
        if (additionalInfoList != null) {
            for (LocationAdditionalInfo info : additionalInfoList) {
                buffer.put(info.encode());
            }
        }

        return buffer.array();
    }

    @Override
    public void decode(byte[] data) throws IllegalArgumentException {
        if (data == null || data.length < 28) {
            throw new IllegalArgumentException("位置信息汇报消息体数据长度不足");
        }

        ByteBuffer buffer = ByteBuffer.wrap(data);
        buffer.order(java.nio.ByteOrder.BIG_ENDIAN);

        // 1. 解码位置基本信息（固定28字节）
        basicInfo = new LocationBasicInfo();
        basicInfo.decode(data);
        buffer.position(28);

        // 2. 解码位置附加信息项列表
        additionalInfoList = new ArrayList<>();
        while (buffer.hasRemaining()) {
            LocationAdditionalInfo info = LocationAdditionalInfo.decode(buffer);
            if (info != null) {
                additionalInfoList.add(info);
            } else {
                break; // 数据不足，停止解析
            }
        }
    }



    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("V2019LocationReportMessageBody{");

        if (basicInfo != null) {
            sb.append("latitude=").append(basicInfo.getLatitudeValue())
              .append("°, longitude=").append(basicInfo.getLongitudeValue())
              .append("°, altitude=").append(basicInfo.getAltitude())
              .append("m, speed=").append(basicInfo.getSpeedValue())
              .append("km/h, direction=").append(basicInfo.getDirection())
              .append("°, time=").append(basicInfo.getTimeAsDateTime());
        }

        if (additionalInfoList != null && !additionalInfoList.isEmpty()) {
            sb.append(", additionalInfoCount=").append(additionalInfoList.size());
        }

        sb.append('}');
        return sb.toString();
    }
}
