package com.zzj.nettydemo.jt808.message.header;

import com.zzj.nettydemo.jt808.protocol.Jt808Version;
import com.zzj.nettydemo.jt808.util.BitUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

/**
 * 2019版本消息头
 */
public class MessageHeaderV2019 extends AbstractMessageHeader {
     {
         this.version = Jt808Version.V2019;
    }

    @Override
    public byte[] encode() {
        ByteBuf buffer = Unpooled.buffer();

        // 第0-1字节：消息ID（2字节）
        buffer.writeShort(messageType);

        // 第2-3字节：消息体属性（2字节）
        int bodyProperty = 0;
        if (properties != null) {
            bodyProperty = properties.encode();
        }
        buffer.writeShort(bodyProperty);

        // 第4字节：协议版本号（1字节）
        buffer.writeByte(0x01); // 2019版本

        // 第5-14字节：终端手机号（10字节，BCD编码）
        byte[] phoneBytes = convertPhoneNumberToBCD(phoneNumber, 10);
        buffer.writeBytes(phoneBytes);

        // 第15-16字节：消息流水号（2字节）
        buffer.writeShort(flowId);

        // 第17-20字节：分包信息（4字节，可选）
        if (properties != null && properties.isHasSubPackage() && packetFragment != null) {
            buffer.writeShort(packetFragment.getTotalPackages());
            buffer.writeShort(packetFragment.getPackageSequence());
        }

        byte[] result = new byte[buffer.readableBytes()];
        buffer.readBytes(result);
        buffer.release();

        return result;
    }

    /**
     * 将手机号转换为BCD编码
     */
    private byte[] convertPhoneNumberToBCD(String phoneNumber, int targetLength) {
        byte[] result = new byte[targetLength];

        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return result;
        }

        // 确保手机号长度为偶数（BCD编码要求）
        String paddedPhone = phoneNumber;
        if (paddedPhone.length() % 2 != 0) {
            paddedPhone = "0" + paddedPhone;
        }

        // 限制最大长度
        int maxChars = targetLength * 2;
        if (paddedPhone.length() > maxChars) {
            paddedPhone = paddedPhone.substring(paddedPhone.length() - maxChars);
        }

        // BCD编码：每个字节包含2个十进制数字
        for (int i = 0; i < paddedPhone.length() && i / 2 < targetLength; i += 2) {
            int high = Character.getNumericValue(paddedPhone.charAt(i));
            int low = 0;
            if (i + 1 < paddedPhone.length()) {
                low = Character.getNumericValue(paddedPhone.charAt(i + 1));
            }
            result[i / 2] = (byte) ((high << 4) | low);
        }

        return result;
    }

    @Override
    public void decode(byte[] data) {
        ByteBuf buffer = Unpooled.copiedBuffer(data);

        // 第0-1字节 2字节
        // 解析消息ID, 消息类型
        messageType = buffer.readUnsignedShort();

        // 第2-3字节 2字节
        // 解析消息体属性
        int attributes = buffer.readUnsignedShort();
        this.properties = MessageProperties.decode(attributes);

        // 第4字节 1字节 解析版本号
        // 协议版本号没用了，工厂创建的时候已经解析了，可以抛弃。
        buffer.readByte();

        // 第5-14字节 10字节
        // 解析终端手机号 (2019版为10字节)
        byte[] phoneBytes = new byte[10];
        buffer.readBytes(phoneBytes);
        phoneNumber = BitUtil.decode(phoneBytes);

        // 第15-16字节 2字节
        // 解析消息流水号
        flowId = buffer.readUnsignedShort();

        // 第17-21字节 4字节(可选)
        // 解析分包信息
        if (properties.isHasSubPackage()) {
            int totalPackages = buffer.readUnsignedShort();
            int packageSequence = buffer.readUnsignedShort();
            packetFragment =new PacketFragment(totalPackages, packageSequence);
        }
    }

    @Override
    public int getHeaderLength() {
        int length = 17;
        if (properties != null && properties.isHasSubPackage()) {
            // 分包信息
            length += 4;
        }
        return length;
    }
}
