package com.zzj.nettydemo.jt808.message.response;

import cn.hutool.core.util.StrUtil;
import com.zzj.nettydemo.jt808.message.Message;
import com.zzj.nettydemo.jt808.message.header.AbstractMessageHeader;
import com.zzj.nettydemo.jt808.message.header.MessageHeaderFactory;
import com.zzj.nettydemo.jt808.message.header.MessageProperties;
import com.zzj.nettydemo.jt808.protocol.Jt808Version;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;

/**
 * 终端注册应答消息构建器 (0x8100)
 * 负责构建符合JT808协议的终端注册应答消息
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
public class TerminalRegisterResponseBuilder {

    /**
     * 注册结果枚举
     */
    public enum RegisterResult {
        SUCCESS(0x00, "成功"),
        VEHICLE_ALREADY_REGISTERED(0x01, "车辆已被注册"),
        NO_SUCH_VEHICLE_IN_DB(0x02, "数据库中无该车辆"),
        TERMINAL_ALREADY_REGISTERED(0x03, "终端已被注册"),
        NO_SUCH_TERMINAL_IN_DB(0x04, "数据库中无该终端");

        private final byte code;
        private final String description;

        RegisterResult(int code, String description) {
            this.code = (byte) code;
            this.description = description;
        }

        public byte getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 构建终端注册应答消息
     *
     * @param originalMessage 原始终端注册请求消息
     * @param result 注册结果
     * @param authCode 鉴权码（仅在注册成功时需要）
     * @return 完整的应答消息字节数组
     */
    public static byte[] buildResponse(Message originalMessage, RegisterResult result, String authCode) {
        try {
            // 1. 构建消息体
            byte[] messageBody = buildResponseBody(originalMessage.getHeader().getFlowId()
                    , result, authCode);

            // 2. 构建消息头
            byte[] messageHeader = buildResponseHeader(originalMessage, messageBody.length);

            // 3. 拼接完整消息
            byte[] fullMessage = new byte[messageHeader.length + messageBody.length];
            System.arraycopy(messageHeader, 0, fullMessage, 0, messageHeader.length);
            System.arraycopy(messageBody, 0, fullMessage, messageHeader.length, messageBody.length);

            log.info("构建终端注册应答消息完成，结果: {}, 消息总长度: {}",
                    result.getDescription(), fullMessage.length);

            return fullMessage;

        } catch (Exception e) {
            log.error("构建终端注册应答消息失败", e);
            throw new RuntimeException("构建终端注册应答消息失败", e);
        }
    }

    /**
     * 构建应答消息体
     * 格式：应答流水号(2字节) + 结果(1字节) + 鉴权码(STRING，仅在成功时存在)
     */
    private static byte[] buildResponseBody(int originalFlowId, RegisterResult result, String authCode) {
        ByteBuf bodyBuffer = Unpooled.buffer();

        // 应答流水号（2字节，对应原始请求的流水号）
        bodyBuffer.writeShort(originalFlowId);

        // 结果（1字节）
        bodyBuffer.writeByte(result.getCode());

        // 鉴权码（仅在注册成功时存在）
        if (result == RegisterResult.SUCCESS && StrUtil.isNotBlank(authCode)) {
            byte[] authCodeBytes = authCode.getBytes();
            bodyBuffer.writeBytes(authCodeBytes);
            log.debug("添加鉴权码: {}", authCode);
        }

        byte[] bodyData = new byte[bodyBuffer.readableBytes()];
        bodyBuffer.readBytes(bodyData);
        bodyBuffer.release();

        return bodyData;
    }

    /**
     * 构建应答消息头
     */
    private static byte[] buildResponseHeader(Message originalMessage, int bodyLength) {
        try {
            // 使用工厂创建对应版本的消息头
            AbstractMessageHeader responseHeader = MessageHeaderFactory.createHeaderByVersion(originalMessage.getVersion());

            // 设置消息ID（0x8100 - 终端注册应答）
            responseHeader.setMessageType(0x8100);

            // 设置消息体属性
            MessageProperties properties = new MessageProperties();
            properties.setBodyLength(bodyLength);
            properties.setUseEncrypt(0);  // 不加密
            properties.setHasSubPackage(false);  // 不分包
            properties.setHasVersionFlag(originalMessage.getVersion() == Jt808Version.V2019);
            properties.setReserved(0);
            responseHeader.setProperties(properties);

            // 设置终端手机号（使用原始消息的手机号）
            responseHeader.setPhoneNumber(originalMessage.getHeader().getPhoneNumber());

            // 设置消息流水号（生成新的流水号）
            responseHeader.setFlowId(generateResponseFlowId());

            // 编码消息头
            return responseHeader.encode();

        } catch (Exception e) {
            log.error("构建应答消息头失败", e);
            throw new RuntimeException("构建应答消息头失败", e);
        }
    }

    /**
     * 生成响应消息的流水号
     * 可以根据实际需求实现更复杂的流水号生成策略
     */
    private static int generateResponseFlowId() {
        return (int) (System.currentTimeMillis() % 65536);
    }

    /**
     * 生成默认的鉴权码
     * 在实际应用中，应该根据安全策略生成更安全的鉴权码
     */
    public static String generateAuthCode() {
        return "AUTH_" + System.currentTimeMillis();
    }

    /**
     * 快速构建成功应答 ,目前鉴权码即为手机号
     */
    public static byte[] buildSuccessResponse(Message originalMessage) {
        String authCode = originalMessage.getHeader().getPhoneNumber();
        return buildResponse(originalMessage, RegisterResult.SUCCESS, authCode);
    }

    /**
     * 快速构建失败应答
     */
    public static byte[] buildFailureResponse(Message originalMessage, RegisterResult result) {
        return buildResponse(originalMessage, result, null);
    }
}
