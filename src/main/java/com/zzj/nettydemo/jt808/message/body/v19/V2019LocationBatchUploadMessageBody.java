package com.zzj.nettydemo.jt808.message.body.v19;

import com.zzj.nettydemo.jt808.message.body.AbstractMessageBody;
import com.zzj.nettydemo.jt808.message.body.MessageBody;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;

import static com.zzj.nettydemo.jt808.protocol.Jt808Version.V2019;

/**
 * <AUTHOR>
 * @date 2025-07-22
 * 定位数据批量上传消息体 (0x0704)
 */
@MessageBody(messageType = 0x0704, version = V2019,
            description = "定位数据批量上传消息体",
            bodyLength = -1)
@Data
@NoArgsConstructor
public class V2019LocationBatchUploadMessageBody extends AbstractMessageBody {

    /**
     * 数据项个数（2字节，WORD类型）
     * 表示当前消息体中所包含的位置汇报数据项的数量，必须大于零
     */
    private short itemCount;

    /**
     * 位置数据类型（1字节，BYTE类型）
     * 0：正常位置批量汇报
     * 1：盲区补报
     */
    private byte locationType;

    /**
     * 位置汇报数据项列表
     */
    private List<LocationReportDataItem> locationDataItems;

    /**
     * 构造函数
     */
    public V2019LocationBatchUploadMessageBody(byte locationType) {
        this.locationType = locationType;
        this.locationDataItems = new ArrayList<>();
    }

    /**
     * 添加位置汇报数据项
     */
    public void addLocationDataItem(V2019LocationReportMessageBody locationReport) {
        if (locationDataItems == null) {
            locationDataItems = new ArrayList<>();
        }

        byte[] locationData = locationReport.encode();
        LocationReportDataItem item = new LocationReportDataItem((short) locationData.length, locationData);
        locationDataItems.add(item);
        updateItemCount();
    }

    /**
     * 添加位置汇报数据项（直接使用字节数据）
     */
    public void addLocationDataItem(byte[] locationData) {
        if (locationDataItems == null) {
            locationDataItems = new ArrayList<>();
        }

        LocationReportDataItem item = new LocationReportDataItem((short) locationData.length, locationData);
        locationDataItems.add(item);
        updateItemCount();
    }

    /**
     * 更新数据项个数
     */
    private void updateItemCount() {
        this.itemCount = (short) (locationDataItems != null ? locationDataItems.size() : 0);
    }

    /**
     * 位置数据类型枚举
     */
    public enum LocationDataType {
        NORMAL_BATCH_REPORT(0, "正常位置批量汇报"),
        BLIND_AREA_SUPPLEMENT(1, "盲区补报");

        private final int value;
        private final String description;

        LocationDataType(int value, String description) {
            this.value = value;
            this.description = description;
        }

        public byte getValue() {
            return (byte) value;
        }

        public String getDescription() {
            return description;
        }

        public static LocationDataType fromValue(byte value) {
            for (LocationDataType type : values()) {
                if (type.getValue() == value) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 位置汇报数据项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocationReportDataItem {

        /**
         * 位置汇报数据体长度（2字节，WORD类型）
         * 用于标识位置汇报数据体的长度
         */
        private short dataLength;

        /**
         * 位置汇报数据体（动态长度，BYTE[n]）
         * 数据格式与位置信息汇报的消息体一致，包含位置基本信息和位置附加信息项列表
         */
        private byte[] dataBody;


        /**
         * 编码位置汇报数据项
         */
        public byte[] encode() {
            ByteBuffer buffer = ByteBuffer.allocate(2 + (dataBody != null ? dataBody.length : 0));
            buffer.order(java.nio.ByteOrder.BIG_ENDIAN);

            // 位置汇报数据体长度（2字节）
            buffer.putShort(dataLength);

            // 位置汇报数据体
            if (dataBody != null) {
                buffer.put(dataBody);
            }

            return buffer.array();
        }

        /**
         * 解码位置汇报数据项
         */
        public static LocationReportDataItem decode(ByteBuffer buffer) {
            if (buffer.remaining() < 2) {
                return null;
            }

            LocationReportDataItem item = new LocationReportDataItem();

            // 位置汇报数据体长度（2字节）
            item.dataLength = buffer.getShort();

            // 位置汇报数据体
            int dataLength = item.dataLength; // 转换为无符号整数
            if (buffer.remaining() >= dataLength) {
                item.dataBody = new byte[dataLength];
                buffer.get(item.dataBody);
            } else {
                throw new IllegalArgumentException("位置汇报数据体长度不足，期望: " + dataLength + ", 实际: " + buffer.remaining());
            }

            return item;
        }

        /**
         * 解析为位置汇报消息体
         */
        public V2019LocationReportMessageBody parseToLocationReport() {
            if (dataBody == null || dataBody.length == 0) {
                return null;
            }

            V2019LocationReportMessageBody locationReport = new V2019LocationReportMessageBody();
            locationReport.decode(dataBody);
            return locationReport;
        }
    }

    @Override
    public byte[] encode() {
        if (locationDataItems == null || locationDataItems.isEmpty()) {
            throw new IllegalStateException("位置汇报数据项列表不能为空");
        }

        updateItemCount();

        // 计算总长度
        // 数据项个数(2字节) + 位置数据类型(1字节)
        int totalLength = 3;
        for (LocationReportDataItem item : locationDataItems) {
            totalLength += 2 + (item.getDataBody() != null ? item.getDataBody().length : 0);
        }

        ByteBuffer buffer = ByteBuffer.allocate(totalLength);
        buffer.order(java.nio.ByteOrder.BIG_ENDIAN);

        // 1. 数据项个数（2字节）
        buffer.putShort(itemCount);

        // 2. 位置数据类型（1字节）
        buffer.put(locationType);

        // 3. 位置汇报数据项列表
        for (LocationReportDataItem item : locationDataItems) {
            buffer.put(item.encode());
        }

        return buffer.array();
    }

    @Override
    public void decode(byte[] data) throws IllegalArgumentException {
        if (data == null || data.length < 3) {
            throw new IllegalArgumentException("定位数据批量上传消息体数据长度不足");
        }

        ByteBuffer buffer = ByteBuffer.wrap(data);
        buffer.order(java.nio.ByteOrder.BIG_ENDIAN);

        // 1. 数据项个数（2字节）
        itemCount = buffer.getShort();
        if (itemCount <= 0) {
            throw new IllegalArgumentException("数据项个数必须大于零，实际值: " + itemCount);
        }

        // 2. 位置数据类型（1字节）
        locationType = buffer.get();

        // 3. 位置汇报数据项列表
        locationDataItems = new ArrayList<>();
        for (int i = 0; i < itemCount; i++) {
            LocationReportDataItem item = LocationReportDataItem.decode(buffer);
            if (item != null) {
                locationDataItems.add(item);
            } else {
                throw new IllegalArgumentException("解析位置汇报数据项失败，索引: " + i);
            }
        }

        // 验证解析的数据项个数
        if (locationDataItems.size() != itemCount) {
            throw new IllegalArgumentException("解析的数据项个数不匹配，期望: " + itemCount + ", 实际: " + locationDataItems.size());
        }
    }

    /**
     * 获取位置数据类型描述
     */
    public String getLocationTypeDescription() {
        LocationDataType type = LocationDataType.fromValue(locationType);
        return type != null ? type.getDescription() : "未知类型(" + locationType + ")";
    }

    /**
     * 获取所有位置汇报数据（解析后的对象列表）
     */
    public List<V2019LocationReportMessageBody> getAllLocationReports() {
        List<V2019LocationReportMessageBody> reports = new ArrayList<>();
        if (locationDataItems != null) {
            for (LocationReportDataItem item : locationDataItems) {
                V2019LocationReportMessageBody report = item.parseToLocationReport();
                if (report != null) {
                    reports.add(report);
                }
            }
        }
        return reports;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("V2019LocationBatchUploadMessageBody{");
        sb.append("itemCount=").append(itemCount);
        sb.append(", locationType=").append(getLocationTypeDescription());

        if (locationDataItems != null && !locationDataItems.isEmpty()) {
            sb.append(", dataItems=[");
            for (int i = 0; i < locationDataItems.size(); i++) {
                if (i > 0) {
                    sb.append(", ");
                }
                LocationReportDataItem item = locationDataItems.get(i);
                sb.append("Item").append(i + 1).append("(length=").append(item.getDataLength()).append(")");
            }
            sb.append("]");
        }

        sb.append('}');
        return sb.toString();
    }
}
