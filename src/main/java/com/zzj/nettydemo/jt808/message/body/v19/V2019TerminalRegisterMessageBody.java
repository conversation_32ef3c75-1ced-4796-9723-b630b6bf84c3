package com.zzj.nettydemo.jt808.message.body.v19;

import com.zzj.nettydemo.jt808.message.body.AbstractMessageBody;
import com.zzj.nettydemo.jt808.message.body.MessageBody;
import com.zzj.nettydemo.jt808.protocol.Jt808Version;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;

import static com.zzj.nettydemo.jt808.protocol.Jt808Version.V2019;

/**
 * <AUTHOR>
 * @date 2025-07-21
 * 终端注册消息体
 */
@MessageBody(messageType = 0x0100, version = V2019,
            description = "终端注册消息体",
             bodyLength = -1)
@Data
@NoArgsConstructor
public class V2019TerminalRegisterMessageBody extends AbstractMessageBody {
    /**
     * 省域ID（起始字节0）
     * 数据类型：WORD（无符号双字节整型）
     * 描述：标示终端安装车辆所在的省域，0保留（由平台取默认值），采用GB/T 2260中行政区划代码前两位
     */
    private int provinceId;

    /**
     * 市县域ID（起始字节2）
     * 数据类型：WORD（无符号双字节整型）
     * 描述：标示终端安装车辆所在的市域和县域，0保留（由平台取默认值），采用GB/T 2260中行政区划代码后四位
     */
    private int cityCountyId;

    /**
     * 制造商ID（起始字节4）
     * 数据类型：BYTE[11]（11字节）
     * 描述：由车载终端厂商所在地行政区划代码和制造商ID组成
     */
    private byte[] manufacturerId;

    /**
     * 终端型号（起始字节15）
     * 数据类型：BYTE[30]（30字节）
     * 描述：由制造商自行定义，位数不足时前补0x00
     */
    private byte[] terminalModel;

    /**
     * 终端ID（起始字节45）
     * 数据类型：BYTE[30]（30字节）
     * 描述：由大写字母和数字组成，制造商自行定义
     */
    private byte[] terminalId;

    /**
     * 车牌颜色（起始字节75）
     * 数据类型：BYTE（单字节）
     * 描述：按照JT/T 697.7-2014规定，未上牌车辆填0
     */
    private byte licensePlateColor;

    /**
     * 车牌（起始字节76）
     * 数据类型：STRING（GBK编码，无数据时置空）
     * 描述：公安交通管理部门颁发的机动车号牌，未上牌则填写车架号
     */
    private String licensePlate;

    @Override
    public byte[] encode() {
        return null;
    }

    /**
     * 将16进制byte数组反序列化为TerminalRegisterMsg对象
     * @param data 符合JT/T 808-2019终端注册消息体格式的byte数组
     * @throws IllegalArgumentException 当字节数组长度不足或格式错误时抛出
     */
    @Override
    public void decode(byte[] data) throws IllegalArgumentException {
        // 最小长度：前76字节为固定字段，车牌部分可变
        if (data == null || data.length < 76) {
            throw new IllegalArgumentException("无效的终端注册消息体数据，长度不足");
        }

        // 大端模式
        ByteBuffer buffer = ByteBuffer.wrap(data).order(java.nio.ByteOrder.BIG_ENDIAN);

        // 1. 省域ID（WORD，2字节，起始0）
        setProvinceId(buffer.getShort() & 0xFFFF);

        // 2. 市县域ID（WORD，2字节，起始2）
        setCityCountyId(buffer.getShort() & 0xFFFF);

        // 3. 制造商ID（BYTE[11]，11字节，起始4）
        byte[] manufacturerId = new byte[11];
        buffer.get(manufacturerId);
        setManufacturerId(manufacturerId);

        // 4. 终端型号（BYTE[30]，30字节，起始15）
        byte[] terminalModel = new byte[30];
        buffer.get(terminalModel);
        setTerminalModel(terminalModel);

        // 5. 终端ID（BYTE[30]，30字节，起始45）
        byte[] terminalId = new byte[30];
        buffer.get(terminalId);
        setTerminalId(terminalId);

        // 6. 车牌颜色（BYTE，1字节，起始75）
        setLicensePlateColor(buffer.get());

        // 7. 车牌（STRING，从76字节开始，剩余所有字节，GBK编码）
        int remaining = buffer.remaining();
        byte[] licensePlateBytes = new byte[remaining];
        buffer.get(licensePlateBytes);
        // 按GBK编码转换，去除末尾可能的空字符
        String licensePlate = new String(licensePlateBytes, Charset.forName("GBK")).trim();
        setLicensePlate(licensePlate.isEmpty() ? null : licensePlate);
    }

}
