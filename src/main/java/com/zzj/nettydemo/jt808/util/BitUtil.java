package com.zzj.nettydemo.jt808.util;


public class BitUtil {


    /**
     *  解析BCD码，转换为十进制字符串，去除前导0
     */
    public static String decode(byte[] bcd) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bcd) {
            sb.append((b >> 4) & 0x0F);
            sb.append(b & 0x0F);
        }

        // 移除前导零
        while (sb.length() > 0 && sb.charAt(0) == '0') {
            sb.deleteCharAt(0);
        }

        return sb.toString();
    }


}
