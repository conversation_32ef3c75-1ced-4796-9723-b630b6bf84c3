package com.zzj.nettydemo.controller;

import com.zzj.nettydemo.domain.vo.LocationInfoVO;
import com.zzj.nettydemo.service.LocationCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-07-22
 * 位置信息查询控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/location")
@CrossOrigin(origins = "*")
public class LocationController {

    @Resource
    private LocationCacheService locationCacheService;

    /**
     * 获取所有车辆的所有位置信息
     */
    @GetMapping("/all")
    public Map<String, Object> getAllLocationInfo() {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, List<LocationInfoVO>> allLocations = locationCacheService.getAllLocationInfo();

            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", allLocations);
            result.put("vehicleCount", allLocations.size());

            // 统计总位置信息数量
            int totalLocationCount = allLocations.values().stream()
                    .mapToInt(List::size)
                    .sum();
            result.put("totalLocationCount", totalLocationCount);

            log.info("查询所有车辆位置信息成功，车辆数: {}, 总位置数: {}", allLocations.size(), totalLocationCount);

        } catch (Exception e) {
            log.error("查询所有车辆位置信息失败", e);
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
            result.put("data", null);
        }

        return result;
    }

    /**
     * 获取所有车辆的最新位置信息
     */
    @GetMapping("/latest")
    public Map<String, Object> getAllLatestLocationInfo() {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, LocationInfoVO> latestLocations = locationCacheService.getAllLatestLocationInfo();

            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", latestLocations);
            result.put("vehicleCount", latestLocations.size());

            log.info("查询所有车辆最新位置信息成功，车辆数: {}", latestLocations.size());

        } catch (Exception e) {
            log.error("查询所有车辆最新位置信息失败", e);
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
            result.put("data", null);
        }

        return result;
    }
}
