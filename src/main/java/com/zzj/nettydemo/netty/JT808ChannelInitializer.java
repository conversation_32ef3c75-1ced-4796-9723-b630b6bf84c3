package com.zzj.nettydemo.netty;

import com.zzj.nettydemo.jt808.codec.JT808Decoder;
import com.zzj.nettydemo.jt808.codec.JT808Encoder;
import com.zzj.nettydemo.jt808.codec.JT808MessageParser;
import com.zzj.nettydemo.jt808.handler.MessagePrintHandler;
import com.zzj.nettydemo.jt808.handler.dispatcher.MessageDispatcher;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.util.concurrent.EventExecutorGroup;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * JT808协议Channel初始化器
 * 负责配置完整的JT808协议处理Pipeline
 * 包含超时处理、原始数据打印、协议解码编码、消息处理等功能
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Slf4j
@Component
public class JT808ChannelInitializer extends ChannelInitializer<SocketChannel> {

    /**
     * 读超时时间（分钟）- 从配置文件读取
     */
    @Value("${netty.read-timeout}")
    private int readTimeOut;

    /**
     * 业务处理线程组 - 用于处理JT808消息的业务逻辑
     */
    @Resource
    private EventExecutorGroup businessGroup;

    /**
     * 消息分发器 - 通过Spring注入
     */
    @Resource
    private MessageDispatcher messageDispatcher;

    /**
     * 写超时时间（秒）- 通常设置为0表示不限制
     */
    private static final int WRITE_TIMEOUT = 0;

    /**
     * 读写超时时间（秒）- 通常设置为0表示不限制
     */
    private static final int ALL_TIMEOUT = 0;

    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        ChannelPipeline pipeline = ch.pipeline();

//        log.debug("初始化JT808 Channel Pipeline for: {}", ch.remoteAddress());

        // ============= 入站处理器链 (Inbound Handlers) =============

        // 1. 连接管理处理器
        pipeline.addLast("connectionManager", ConnectionManagerHandler.forJT808Server());

        // 3. 原始数据十六进制打印处理器
        pipeline.addLast("hexDumpHandler", new HexDumpHandler("终端上传数据包"));

        // 4. JT808解码器 - 处理拆包粘包分包
        pipeline.addLast("jt808Decoder", new JT808Decoder());

        // 5. 解码后数据打印处理器
        pipeline.addLast("decodedHexDumpHandler", new HexDumpHandler("解码数据包"));

        // 6. JT808消息协议解析器 - 将字节数组解析为Message对象
        pipeline.addLast("jt808MessageParser", new JT808MessageParser());

        // 7. 消息打印处理器 - 打印解析后的消息内容
        pipeline.addLast("messagePrintHandler", new MessagePrintHandler("解析后的消息"));



        // ============= 出站处理器链 (Outbound Handlers) =============

        // 9. JT808消息编码器 - 将byte[]消息编码为JT808协议格式
        pipeline.addLast("jt808Encoder", new JT808Encoder());

        // 10. 响应消息打印处理器 - 打印将要发送的响应消息
        pipeline.addLast("responsePrintHandler", new HexDumpHandler("响应数据包"));

        // 8. 消息分发处理器 - 异步处理业务逻辑（使用业务线程组）
        pipeline.addLast("messageDispatcher", messageDispatcher);

//        log.info("JT808 Channel Pipeline 初始化完成");
    }
}
