package com.zzj.nettydemo.netty;

import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 连接管理处理器
 * 专门负责处理Netty连接的创建、关闭和异常管理
 * 提供连接统计和监控功能
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
public class ConnectionManagerHandler extends ChannelInboundHandlerAdapter {

    // 连接统计
    private static final AtomicLong totalConnections = new AtomicLong(0);
    private static final AtomicLong activeConnections = new AtomicLong(0);
    private static final AtomicLong totalExceptions = new AtomicLong(0);

    private final String serverName;
    private final boolean enableDetailedLogging;

    /**
     * 默认构造函数
     */
    public ConnectionManagerHandler() {
        this("Netty服务器", true);
    }

    /**
     * 自定义服务器名称构造函数
     * 
     * @param serverName 服务器名称，用于日志标识
     */
    public ConnectionManagerHandler(String serverName) {
        this(serverName, true);
    }

    /**
     * 完整构造函数
     * 
     * @param serverName 服务器名称
     * @param enableDetailedLogging 是否启用详细日志
     */
    public ConnectionManagerHandler(String serverName, boolean enableDetailedLogging) {
        this.serverName = serverName != null ? serverName : "Netty服务器";
        this.enableDetailedLogging = enableDetailedLogging;
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        String clientAddress = getClientAddress(ctx);
        String channelId = ctx.channel().id().asShortText();
        
        // 更新连接统计
        long total = totalConnections.incrementAndGet();
        long active = activeConnections.incrementAndGet();
        
        if (enableDetailedLogging) {
            log.info("🔗 [{}] 连接建立 - 通道ID: [{}], 客户端: {}, 当前活跃连接: {}, 总连接数: {}", 
                    serverName, channelId, clientAddress, active, total);
        } else {
            log.info("连接建立 [{}] 客户端: {}", channelId, clientAddress);
        }
        
        // 传递事件到下一个处理器
        ctx.fireChannelActive();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        String clientAddress = getClientAddress(ctx);
        String channelId = ctx.channel().id().asShortText();
        
        // 更新连接统计
        long active = activeConnections.decrementAndGet();
        
        if (enableDetailedLogging) {
            log.info("❌ [{}] 连接断开 - 通道ID: [{}], 客户端: {}, 当前活跃连接: {}", 
                    serverName, channelId, clientAddress, active);
        } else {
            log.info("连接断开 [{}] 客户端: {}", channelId, clientAddress);
        }
        
        // 传递事件到下一个处理器
        ctx.fireChannelInactive();
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        String clientAddress = getClientAddress(ctx);
        String channelId = ctx.channel().id().asShortText();
        
        // 更新异常统计
        long exceptions = totalExceptions.incrementAndGet();
        
        if (enableDetailedLogging) {
            log.error("⚠️ [{}] 连接异常 - 通道ID: [{}], 客户端: {}, 异常类型: {}, 异常信息: {}, 总异常数: {}", 
                    serverName, channelId, clientAddress, 
                    cause.getClass().getSimpleName(), cause.getMessage(), exceptions);
        } else {
            log.error("连接异常 [{}] 客户端: {} 异常: {}", channelId, clientAddress, cause.getMessage());
        }
        
        // 传递异常到下一个处理器
        ctx.fireExceptionCaught(cause);
    }

    /**
     * 获取客户端地址，处理可能的空指针异常
     */
    private String getClientAddress(ChannelHandlerContext ctx) {
        try {
            return ctx.channel().remoteAddress() != null ? 
                   ctx.channel().remoteAddress().toString() : "未知地址";
        } catch (Exception e) {
            return "地址获取失败";
        }
    }

    /**
     * 获取当前活跃连接数
     */
    public static long getActiveConnections() {
        return activeConnections.get();
    }

    /**
     * 获取总连接数
     */
    public static long getTotalConnections() {
        return totalConnections.get();
    }

    /**
     * 获取总异常数
     */
    public static long getTotalExceptions() {
        return totalExceptions.get();
    }

    /**
     * 重置统计信息
     */
    public static void resetStatistics() {
        totalConnections.set(0);
        activeConnections.set(0);
        totalExceptions.set(0);
        log.info("连接统计信息已重置");
    }

    /**
     * 打印当前连接统计信息
     */
    public static void printStatistics() {
        log.info("📊 连接统计 - 活跃连接: {}, 总连接数: {}, 总异常数: {}", 
                activeConnections.get(), totalConnections.get(), totalExceptions.get());
    }

    /**
     * 便利方法：创建用于JT808服务器的连接管理器
     */
    public static ConnectionManagerHandler forJT808Server() {
        return new ConnectionManagerHandler("JT808服务器", true);
    }

    /**
     * 便利方法：创建简单的连接管理器（仅基础日志）
     */
    public static ConnectionManagerHandler simple() {
        return new ConnectionManagerHandler("服务器", false);
    }
}
