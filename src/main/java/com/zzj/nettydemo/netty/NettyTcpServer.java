package com.zzj.nettydemo.netty;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.util.ResourceLeakDetector;
import io.netty.util.concurrent.EventExecutorGroup;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * JT808服务器
 * 负责启动和管理Netty服务器
 * 支持两种模式：
 * 1. 原始数据模式 - 只打印原始数据包，不进行任何处理
 * 2. 完整处理模式 - 解码、处理和响应JT808消息
 */
@Slf4j
@Component
public class NettyTcpServer {

    @Value("${netty.port}")
    private int port;

    @Resource
    private NioEventLoopGroup bossGroup;

    @Resource
    private NioEventLoopGroup workerGroup;

    @Resource
    private EventExecutorGroup businessGroup;

    @Resource
    private JT808ChannelInitializer jt808ChannelInitializer;

    /**
     * 用于启动Netty服务器的线程池
     */
    private final ExecutorService nettyStartupExecutor = Executors.newSingleThreadExecutor(r -> {
        Thread thread = new Thread(r, "netty-startup-thread");
        thread.setDaemon(false); // 设置为非守护线程，确保服务器正常运行
        return thread;
    });

    /**
     * Netty服务器的ChannelFuture，用于管理服务器生命周期
     */
    private volatile ChannelFuture serverChannelFuture;

    /**
     * 服务器启动状态
     */
    private volatile boolean isStarted = false;

    /**
     * 启动服务器
     */
    @PostConstruct
    public void start() {
        log.info("准备在独立线程中启动Netty TCP服务器...");

        // 使用CompletableFuture在独立线程中启动服务器
        CompletableFuture.runAsync(this::startNettyServer, nettyStartupExecutor)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("Netty服务器启动失败", throwable);
                    }
                });
    }

    /**
     * 在独立线程中启动Netty服务器的具体实现
     */
    private void startNettyServer() {
        try {
            ServerBootstrap serverBootstrap = new ServerBootstrap();
            serverBootstrap
                    // 指定使用的线程组
                    .group(bossGroup, workerGroup)
                    // 指定使用的通道
                    .channel(NioServerSocketChannel.class)
                    .childHandler(jt808ChannelInitializer)
                    //服务端可连接队列数,对应TCP/IP协议listen函数中backlog参数
                    .option(ChannelOption.SO_BACKLOG, 1024)
                    //保持长连接
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    //立即写出
                    .childOption(ChannelOption.TCP_NODELAY, true);

            //内存泄漏检测 开发推荐PARANOID 线上SIMPLE
            ResourceLeakDetector.setLevel(ResourceLeakDetector.Level.PARANOID);

            log.info("正在启动Netty TCP服务器，端口: {}", port);

            // 绑定端口并启动服务器
            serverChannelFuture = serverBootstrap.bind(port).sync();

            if (serverChannelFuture.isSuccess()) {
                isStarted = true;
                log.info("Netty TCP服务器启动成功！端口: {}, 线程: {}", port, Thread.currentThread().getName());

                // 等待服务器关闭
                serverChannelFuture.channel().closeFuture().sync();

            } else {
                log.error("Netty TCP服务器启动失败，端口: {}", port);
                throw new RuntimeException("服务器启动失败");
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Netty TCP服务器启动被中断", e);
            throw new RuntimeException("服务器启动被中断", e);
        } catch (Exception e) {
            log.error("Netty TCP服务器启动异常", e);
            throw new RuntimeException("服务器启动异常", e);
        } finally {
            isStarted = false;
            log.info("Netty TCP服务器已停止");
        }
    }

    /**
     * 手动停止服务器
     */
    public void stop() {
        if (serverChannelFuture != null && serverChannelFuture.channel().isActive()) {
            try {
                log.info("正在停止Netty TCP服务器...");
                serverChannelFuture.channel().close().sync();
                log.info("Netty TCP服务器已停止");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("停止服务器时被中断", e);
            }
        }
    }

    /**
     * 关闭服务器,销毁资源
     */
    @PreDestroy
    public void shutdown() {
        log.info("开始关闭Netty TCP服务器资源...");

        // 停止服务器
        stop();

        // 关闭线程池
        if (!nettyStartupExecutor.isShutdown()) {
            nettyStartupExecutor.shutdown();
            log.info("Netty启动线程池已关闭");
        }

        // 关闭事件循环组
        bossGroup.shutdownGracefully().syncUninterruptibly();
        workerGroup.shutdownGracefully().syncUninterruptibly();
        businessGroup.shutdownGracefully().syncUninterruptibly();

        log.info("Netty TCP服务器资源关闭完成");
    }
}
