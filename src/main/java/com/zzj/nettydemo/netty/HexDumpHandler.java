package com.zzj.nettydemo.netty;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;

/**
 * 十六进制数据打印处理器
 * 专门用于在Netty处理器链中打印十六进制格式的数据包
 * 支持自定义标题、自动计算字节数量，便于在多个位置复用
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Slf4j
public class HexDumpHandler extends ChannelInboundHandlerAdapter {

    private static final int BYTES_PER_LINE = 16;
    private static final String HEX_HEADER = " 0  1  2  3  4  5  6  7  8  9  a  b  c  d  e  f ";

    private final String customTitle;

    public HexDumpHandler() {
        this("数据包");
    }

    public HexDumpHandler(String customTitle) {
        this.customTitle = customTitle != null ? customTitle : "数据包";
    }

    /**
     * 静态方法：格式化ByteBuf为十六进制表格
     * 自动计算传入的字节数量并生成格式化的十六进制输出
     *
     * @param buffer ByteBuf数据缓冲区
     * @param title 自定义标题
     * @param clientAddress 客户端地址
     * @param channelId 通道ID
     * @return 格式化的十六进制字符串
     */
    public static String formatHexDump(ByteBuf buffer, String title, String clientAddress, String channelId) {
        if (buffer == null || !buffer.isReadable()) {
            return formatEmptyPacket(title, clientAddress, channelId);
        }

        // 检查ByteBuf的引用计数，避免访问已释放的缓冲区
        if (buffer.refCnt() <= 0) {
            log.warn("尝试访问已释放的ByteBuf，跳过打印");
            return formatEmptyPacket(title + " [已释放]", clientAddress, channelId);
        }

        int length = buffer.readableBytes();
        int rows = (length + BYTES_PER_LINE - 1) / BYTES_PER_LINE;

        // 计算表格宽度
        int maxInfoWidth = Math.max(
                getInfoLineWidth("标题", title),
                Math.max(
                        getInfoLineWidth("客户端", clientAddress),
                        Math.max(
                                getInfoLineWidth("字节数", length + " 字节"),
                                getInfoLineWidth("通道ID", channelId)
                        )
                )
        );
        int tableWidth = Math.max(maxInfoWidth, HEX_HEADER.length()) + 4;

        StringBuilder dump = new StringBuilder();

        // 构建表格
        appendBorderLine(dump, tableWidth);
        appendPaddedInfoLine(dump, tableWidth, "标题", title);
        appendPaddedInfoLine(dump, tableWidth, "客户端", clientAddress);
        appendPaddedInfoLine(dump, tableWidth, "字节数", length + " 字节");
        appendPaddedInfoLine(dump, tableWidth, "通道ID", channelId);
        appendBorderLine(dump, tableWidth);
        appendCenteredLine(dump, tableWidth);
        appendBorderLine(dump, tableWidth);

        // 生成数据行
        try {
            for (int row = 0; row < rows; row++) {
                int offset = row * BYTES_PER_LINE;
                int lineLength = Math.min(BYTES_PER_LINE, length - offset);

                StringBuilder line = new StringBuilder("| ");
                for (int i = 0; i < BYTES_PER_LINE; i++) {
                    if (i < lineLength) {
                        // 安全地读取字节，避免访问已释放的缓冲区
                        byte b = buffer.getByte(offset + i);
                        line.append(String.format("%02x ", b & 0xFF));
                    } else {
                        line.append("   ");
                    }
                }
                line.append(" |");
                dump.append(String.format("%-" + tableWidth + "s\n", line));
            }
        } catch (Exception e) {
            // 如果访问ByteBuf时发生异常，返回错误信息
            log.warn("读取ByteBuf数据时发生异常: {}", e.getMessage());
            return formatEmptyPacket(title + " [读取失败]", clientAddress, channelId);
        }

        appendBorderLine(dump, tableWidth);
        return dump.toString();
    }

    /**
     * 静态方法：格式化字节数组为十六进制表格
     * 自动计算传入的字节数量并生成格式化的十六进制输出
     *
     * @param data 字节数组
     * @param title 自定义标题
     * @param clientAddress 客户端地址
     * @param channelId 通道ID
     * @return 格式化的十六进制字符串
     */
    public static String formatHexDump(byte[] data, String title, String clientAddress, String channelId) {
        if (data == null || data.length == 0) {
            return formatEmptyPacket(title, clientAddress, channelId);
        }

        ByteBuf buffer = Unpooled.wrappedBuffer(data);
        try {
            return formatHexDump(buffer, title, clientAddress, channelId);
        } finally {
            buffer.release();
        }
    }

    private static String formatEmptyPacket(String title, String clientAddress, String channelId) {
        int tableWidth = 50;
        StringBuilder dump = new StringBuilder();
        appendBorderLine(dump, tableWidth);
        appendPaddedInfoLine(dump, tableWidth, "标题", title);
        appendPaddedInfoLine(dump, tableWidth, "客户端", clientAddress);
        appendPaddedInfoLine(dump, tableWidth, "字节数", "0 字节");
        appendPaddedInfoLine(dump, tableWidth, "通道ID", channelId);
        appendBorderLine(dump, tableWidth);
        return dump.toString();
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        if (msg instanceof ByteBuf byteBuf) {
            try {
                String clientAddress = getClientAddress(ctx);
                String channelId = ctx.channel().id().asShortText();

                String hexDump = formatHexDump(byteBuf, customTitle, clientAddress, channelId);
                log.info("\n{}", hexDump);
            } catch (Exception e) {
                log.warn("打印十六进制数据时发生异常: {}", e.getMessage());
            }
        }

        ctx.fireChannelRead(msg);
    }

    /**
     * 安全获取客户端地址
     */
    private String getClientAddress(ChannelHandlerContext ctx) {
        try {
            return ctx.channel().remoteAddress() != null ?
                   ctx.channel().remoteAddress().toString() : "未知地址";
        } catch (Exception e) {
            return "地址获取失败";
        }
    }

    /**
     * 便利方法：快速打印ByteBuf数据
     *
     * @param buffer 数据缓冲区
     * @param title 标题
     */
    public static void printHexDump(ByteBuf buffer, String title) {
        if (buffer != null && buffer.isReadable()) {
            String hexDump = formatHexDump(buffer, title, "N/A", "N/A");
            log.info("\n{}", hexDump);
        }
    }

    /**
     * 快速打印字节数组
     *
     * @param data 字节数组
     * @param title 标题
     */
    public static void printHexDump(byte[] data, String title) {
        if (data != null && data.length > 0) {
            String hexDump = formatHexDump(data, title, "N/A", "N/A");
            log.info("\n{}", hexDump);
        }
    }

    private static int getInfoLineWidth(String label, String value) {
        return ("| " + label + ": " + value + " |").length();
    }

    private static void appendBorderLine(StringBuilder dump, int width) {
        dump.append("+").append("-".repeat(width - 2)).append("+\n");
    }

    private static void appendPaddedInfoLine(StringBuilder dump, int width, String label, String value) {
        String line = "| " + label + ": " + value;
        int padding = width - line.length() - 1;
        if (padding > 0) {
            line += " ".repeat(padding);
        }
        dump.append(line).append("|\n");
    }

    private static void appendCenteredLine(StringBuilder dump, int width) {
        int padding = (width - HexDumpHandler.HEX_HEADER.length() - 2) / 2;
        StringBuilder line = new StringBuilder("|");
        line.append(" ".repeat(padding)).append(HexDumpHandler.HEX_HEADER);
        while (line.length() < width - 1) {
            line.append(" ");
        }
        line.append("|");
        dump.append(line).append("\n");
    }

}
