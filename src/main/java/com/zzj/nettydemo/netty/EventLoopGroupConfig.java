package com.zzj.nettydemo.netty;

import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.util.concurrent.DefaultEventExecutorGroup;
import io.netty.util.concurrent.EventExecutorGroup;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025-07-16
 */
@Configuration
public class EventLoopGroupConfig {

    @Value("${netty.threads.boss}")
    private int bossThreadsNum;

    @Value("${netty.threads.worker}")
    private int workerThreadsNum;

    @Value("${netty.threads.business}")
    private int businessThreadsNum;

    /**
     * 负责TCP连接建立操作 绝对不能阻塞
     */
    @Bean(name = "bossGroup")
    public NioEventLoopGroup bossGroup() {
        return new NioEventLoopGroup(bossThreadsNum);
    }

    /**
     * 负责Socket读写操作 绝对不能阻塞
     */
    @Bean(name = "workerGroup")
    public NioEventLoopGroup workGroup(){
        return new NioEventLoopGroup(workerThreadsNum);
    }

    /**
     * Hanlder出现IO操作(数据库，网络操作等），使用这个
     */
    @Bean(name = "businessGroup")
    public EventExecutorGroup businessGroup() {
        return new DefaultEventExecutorGroup(businessThreadsNum);
    }
}
