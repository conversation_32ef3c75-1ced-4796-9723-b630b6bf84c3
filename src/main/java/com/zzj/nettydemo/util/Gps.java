package com.zzj.nettydemo.util;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025-07-22
 * GPS坐标实体类
 */
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
public class Gps {
    private double latitude;
    private double longitude;

    @Override
    public String toString() {
        return "Gps{" +
                "latitude=" + latitude +
                ", longitude=" + longitude +
                '}';
    }
}
