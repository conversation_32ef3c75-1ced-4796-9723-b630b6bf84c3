package com.zzj.nettydemo.service;

import com.zzj.nettydemo.domain.vo.LocationInfoVO;
import com.zzj.nettydemo.jt808.message.body.v19.V2019LocationReportMessageBody;
import com.zzj.nettydemo.util.CoordinateTransformUtil;
import com.zzj.nettydemo.util.Gps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025-07-22
 * 位置信息缓存服务
 */
@Slf4j
@Service
public class LocationCacheService {

    /**
     * 位置信息缓存
     * Key: 终端手机号
     * Value: 该终端的位置信息列表（按时间倒序）
     */
    private final Map<String, List<LocationInfoVO>> locationCache = new ConcurrentHashMap<>();

    /**
     * 每个终端最大缓存的位置信息数量
     */
    private static final int MAX_LOCATIONS_PER_TERMINAL = 1000;

    /**
     * 添加单个位置信息
     */
    public void addLocationInfo(String phoneNumber, V2019LocationReportMessageBody locationReport) {
        if (phoneNumber == null || locationReport == null || locationReport.getBasicInfo() == null) {
            log.warn("位置信息参数不完整，忽略保存");
            return;
        }

        LocationInfoVO locationVO = convertToLocationVO(phoneNumber, locationReport);
        addLocationInfo(locationVO);
    }

    /**
     * 批量添加位置信息
     */
    public void addLocationInfoBatch(String phoneNumber, List<V2019LocationReportMessageBody> locationReports) {
        if (phoneNumber == null || locationReports == null || locationReports.isEmpty()) {
            log.warn("批量位置信息参数不完整，忽略保存");
            return;
        }

        List<LocationInfoVO> locationVoList = new LinkedList<>();
        for (V2019LocationReportMessageBody locationReport : locationReports) {
            if (locationReport != null && locationReport.getBasicInfo() != null) {
                LocationInfoVO locationVO = convertToLocationVO(phoneNumber, locationReport);
                locationVoList.add(locationVO);
            }
        }

        if (!locationVoList.isEmpty()) {
            addLocationInfoBatch(locationVoList);
            log.info("批量保存位置信息成功，终端: {}, 数量: {}", phoneNumber, locationVoList.size());
        }
    }

    /**
     * 添加位置信息VO
     */
    private void addLocationInfo(LocationInfoVO locationVO) {
        String phoneNumber = locationVO.getPhoneNumber();

        locationCache.computeIfAbsent(phoneNumber, k -> Collections.synchronizedList(new LinkedList<>()));
        List<LocationInfoVO> locations = locationCache.get(phoneNumber);

        synchronized (locations) {
            // 添加到列表开头（最新的在前面）
            locations.add(0, locationVO);

            // 限制缓存数量
            if (locations.size() > MAX_LOCATIONS_PER_TERMINAL) {
                locations.subList(MAX_LOCATIONS_PER_TERMINAL, locations.size()).clear();
            }
        }

        log.debug("保存位置信息成功，终端: {}, 当前缓存数量: {}", phoneNumber, locations.size());
    }

    /**
     * 批量添加位置信息VO
     */
    private void addLocationInfoBatch(List<LocationInfoVO> locationVOs) {
        if (locationVOs.isEmpty()) {
            return;
        }

        // 按手机号分组
        Map<String, List<LocationInfoVO>> groupedLocations = new HashMap<>();
        for (LocationInfoVO locationVO : locationVOs) {
            groupedLocations.computeIfAbsent(locationVO.getPhoneNumber(), k -> new LinkedList<>()).add(locationVO);
        }

        // 批量添加到缓存
        for (Map.Entry<String, List<LocationInfoVO>> entry : groupedLocations.entrySet()) {
            String phoneNumber = entry.getKey();
            List<LocationInfoVO> locations = entry.getValue();

            locationCache.computeIfAbsent(phoneNumber, k -> Collections.synchronizedList(new LinkedList<>()));
            List<LocationInfoVO> cachedLocations = locationCache.get(phoneNumber);

            synchronized (cachedLocations) {
                // 按时间倒序排序（最新的在前面）
                locations.sort((a, b) -> b.getTime().compareTo(a.getTime()));

                // 添加到列表开头
                cachedLocations.addAll(0, locations);

                // 限制缓存数量
                if (cachedLocations.size() > MAX_LOCATIONS_PER_TERMINAL) {
                    cachedLocations.subList(MAX_LOCATIONS_PER_TERMINAL, cachedLocations.size()).clear();
                }
            }
        }
    }

    /**
     * 转换为LocationInfoVO（包含坐标转换）
     */
    private LocationInfoVO convertToLocationVO(String phoneNumber, V2019LocationReportMessageBody locationReport) {
        V2019LocationReportMessageBody.LocationBasicInfo basicInfo = locationReport.getBasicInfo();

        LocationInfoVO locationVO = new LocationInfoVO();
        locationVO.setPhoneNumber(phoneNumber);

        // 原始WGS84坐标
        double wgs84Lat = basicInfo.getLatitudeValue();
        double wgs84Lon = basicInfo.getLongitudeValue();
        locationVO.setLatitude(wgs84Lat);
        locationVO.setLongitude(wgs84Lon);

        // 转换为GCJ02坐标（火星坐标系）
        try {
            Gps gcj02Coords = CoordinateTransformUtil.wgs84ToGcj02(wgs84Lat, wgs84Lon);
            if (gcj02Coords != null) {
                locationVO.setGcj02Latitude(gcj02Coords.getLatitude());
                locationVO.setGcj02Longitude(gcj02Coords.getLongitude());
                log.debug("坐标转换成功 - 终端: {}, WGS84({}, {}) -> GCJ02({}, {})",
                         phoneNumber, wgs84Lat, wgs84Lon,
                         gcj02Coords.getLatitude(), gcj02Coords.getLongitude());
            } else {
                // 转换失败，使用原始坐标
                locationVO.setGcj02Latitude(wgs84Lat);
                locationVO.setGcj02Longitude(wgs84Lon);
                log.warn("坐标转换失败，使用原始坐标 - 终端: {}", phoneNumber);
            }
        } catch (Exception e) {
            // 转换异常，使用原始坐标
            locationVO.setGcj02Latitude(wgs84Lat);
            locationVO.setGcj02Longitude(wgs84Lon);
            log.error("坐标转换异常，使用原始坐标 - 终端: {}", phoneNumber, e);
        }

        locationVO.setAltitude(basicInfo.getAltitude());
        locationVO.setSpeed(basicInfo.getSpeedValue());
        locationVO.setDirection(basicInfo.getDirection());
        locationVO.setTime(basicInfo.getTimeAsDateTime());
        locationVO.setAlarmFlag(basicInfo.getAlarmFlag());
        locationVO.setStatus(basicInfo.getStatus());
        locationVO.setReceivedTime(LocalDateTime.now());

        return locationVO;
    }

    /**
     * 获取所有车辆的所有位置信息
     * @return Key: 终端手机号, Value: 该终端的所有位置信息列表
     */
    public Map<String, List<LocationInfoVO>> getAllLocationInfo() {
        Map<String, List<LocationInfoVO>> result = new HashMap<>();

        for (Map.Entry<String, List<LocationInfoVO>> entry : locationCache.entrySet()) {
            String phoneNumber = entry.getKey();
            List<LocationInfoVO> locations = entry.getValue();

            synchronized (locations) {
                // 创建副本避免并发修改
                result.put(phoneNumber, new ArrayList<>(locations));
            }
        }

        log.info("查询所有车辆位置信息，车辆数: {}", result.size());
        return result;
    }

    /**
     * 获取所有车辆的最新位置信息
     * @return Key: 终端手机号, Value: 该终端的最新位置信息
     */
    public Map<String, LocationInfoVO> getAllLatestLocationInfo() {
        Map<String, LocationInfoVO> result = new HashMap<>();

        for (Map.Entry<String, List<LocationInfoVO>> entry : locationCache.entrySet()) {
            String phoneNumber = entry.getKey();
            List<LocationInfoVO> locations = entry.getValue();

            synchronized (locations) {
                if (!locations.isEmpty()) {
                    // 第一个元素是最新的位置信息（因为新位置总是添加到列表开头）
                    result.put(phoneNumber, locations.get(0));
                }
            }
        }

        log.info("查询所有车辆最新位置信息，车辆数: {}", result.size());
        return result;
    }

}
