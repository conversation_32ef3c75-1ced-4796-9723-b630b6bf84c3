spring:
  application:
    name: nettyDemo
  datasource:
    url: *********************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
  config:
    import:
      - classpath:message-config.yml


# JT808服务器配置
netty:
  port: 13000
  read-timeout: 15 #读超时 15分钟
  threads:
    boss: 1
    worker: 4
    business: 20 #业务线程数量

# 日志配置
logging:
  level:
    com.zzj.nettydemo.jt808: INFO
    io.netty: INFO
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{20} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/netty-jt808.log
  logback:
    rolling policy:
      max-file-size: 100MB
      max-history: 7
server:
  port: 8087

